<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-title {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .test-section h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .test-input {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .test-expected {
            color: #666;
            font-size: 14px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .link-test {
            margin-top: 20px;
            padding: 20px;
            background: #e6f7ff;
            border-radius: 8px;
        }
        
        .link-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .link-path {
            font-family: monospace;
            color: #1890ff;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .status.pending {
            background: #fff7e6;
            color: #fa8c16;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🧪 智能助手测试页面</h1>
            <p>测试智能助手的链接生成和导航功能</p>
        </div>
        
        <div class="test-section">
            <h3>📝 测试用例</h3>
            <p>以下是常见的用户询问，点击按钮可以快速测试智能助手的回复：</p>
            
            <div class="test-case">
                <div class="test-input">用户输入："我记忆力很差，经常忘事"</div>
                <div class="test-expected">期望：推荐记忆力训练链接</div>
                <button class="test-button" onclick="testQuery('我记忆力很差，经常忘事')">测试</button>
            </div>
            
            <div class="test-case">
                <div class="test-input">用户输入："说话有困难，表达不清楚"</div>
                <div class="test-expected">期望：推荐语言功能训练链接</div>
                <button class="test-button" onclick="testQuery('说话有困难，表达不清楚')">测试</button>
            </div>
            
            <div class="test-case">
                <div class="test-input">用户输入："注意力不集中，容易分心"</div>
                <div class="test-expected">期望：推荐注意力训练链接</div>
                <button class="test-button" onclick="testQuery('注意力不集中，容易分心')">测试</button>
            </div>
            
            <div class="test-case">
                <div class="test-input">用户输入："心情不好，很抑郁"</div>
                <div class="test-expected">期望：推荐情绪调节训练链接</div>
                <button class="test-button" onclick="testQuery('心情不好，很抑郁')">测试</button>
            </div>
            
            <div class="test-case">
                <div class="test-input">用户输入："想和其他病友交流经验"</div>
                <div class="test-expected">期望：推荐老人论坛链接</div>
                <button class="test-button" onclick="testQuery('想和其他病友交流经验')">测试</button>
            </div>
            
            <div class="test-case">
                <div class="test-input">用户输入："想学习康复知识"</div>
                <div class="test-expected">期望：推荐短视频频道链接</div>
                <button class="test-button" onclick="testQuery('想学习康复知识')">测试</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 链接有效性测试</h3>
            <p>检查平台所有功能模块的链接是否有效：</p>
            <button class="test-button" onclick="testAllLinks()">测试所有链接</button>
            
            <div class="link-test" id="linkTestResults">
                <p>点击上方按钮开始测试...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="testResults">
                <p>暂无测试结果</p>
            </div>
        </div>
    </div>
    
    <script>
        // 平台链接映射
        const platformLinks = {
            '主页': '/home/<USER>',
            '训练康复': '/training/index.html',
            '认知功能训练': '/training/cognitive_function_training/index.html',
            '注意力训练': '/training/cognitive_function_training/attention_training/index.html',
            '记忆力训练': '/training/cognitive_function_training/memory_training/index.html',
            '执行功能训练': '/training/cognitive_function_training/executive_function_training/index.html',
            '语言功能训练': '/training/language_function_training/index.html',
            '视觉障碍训练': '/training/visual_impairment_training/index.html',
            '情绪调节训练': '/training/emotional_regulation_training/index.html',
            '量表评估': '/assessment/index.html',
            '老人论坛': '/forum/index.html',
            '短视频频道': '/videos/index.html',
            '积分系统': '/points/index.html'
        };
        
        // 测试查询
        function testQuery(query) {
            // 模拟智能助手回复
            const response = generateMockResponse(query);
            displayTestResult(query, response);
            
            // 如果智能助手已加载，也可以实际测试
            if (window.aiAssistant) {
                window.aiAssistant.openChat();
                setTimeout(() => {
                    document.getElementById('aiMessageInput').value = query;
                    window.aiAssistant.sendMessage();
                }, 500);
            }
        }
        
        // 生成模拟回复
        function generateMockResponse(query) {
            const responses = {
                '记忆力': {
                    text: '根据您的需求，我推荐记忆力训练功能',
                    links: ['/training/cognitive_function_training/memory_training/index.html']
                },
                '说话': {
                    text: '根据您的需求，我推荐语言功能训练',
                    links: ['/training/language_function_training/index.html']
                },
                '注意力': {
                    text: '根据您的需求，我推荐注意力训练功能',
                    links: ['/training/cognitive_function_training/attention_training/index.html']
                },
                '心情': {
                    text: '根据您的需求，我推荐情绪调节训练',
                    links: ['/training/emotional_regulation_training/index.html']
                },
                '交流': {
                    text: '根据您的需求，我推荐老人论坛功能',
                    links: ['/forum/index.html']
                },
                '学习': {
                    text: '根据您的需求，我推荐短视频频道',
                    links: ['/videos/index.html']
                }
            };
            
            for (const [key, response] of Object.entries(responses)) {
                if (query.includes(key)) {
                    return response;
                }
            }
            
            return {
                text: '我理解您的需求，为您推荐相关功能',
                links: ['/home/<USER>']
            };
        }
        
        // 显示测试结果
        function displayTestResult(query, response) {
            const resultsDiv = document.getElementById('testResults');
            const resultHtml = `
                <div class="test-case">
                    <div class="test-input">查询: "${query}"</div>
                    <div class="test-expected">回复: ${response.text}</div>
                    <div class="test-expected">生成链接: ${response.links.join(', ')}</div>
                    <div style="margin-top: 10px;">
                        ${response.links.map(link => 
                            `<a href="${link}" target="_blank" style="color: #1890ff; margin-right: 10px;">${link}</a>`
                        ).join('')}
                    </div>
                </div>
            `;
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }
        
        // 测试所有链接
        async function testAllLinks() {
            const resultsDiv = document.getElementById('linkTestResults');
            resultsDiv.innerHTML = '<p>正在测试链接...</p>';
            
            const results = [];
            
            for (const [name, path] of Object.entries(platformLinks)) {
                try {
                    const response = await fetch(path, { method: 'HEAD' });
                    results.push({
                        name,
                        path,
                        status: response.ok ? 'success' : 'error',
                        message: response.ok ? '可访问' : `错误: ${response.status}`
                    });
                } catch (error) {
                    results.push({
                        name,
                        path,
                        status: 'error',
                        message: '无法访问'
                    });
                }
            }
            
            // 显示结果
            const resultHtml = results.map(result => `
                <div class="link-item">
                    <div>
                        <strong>${result.name}</strong><br>
                        <span class="link-path">${result.path}</span>
                    </div>
                    <span class="status ${result.status}">${result.message}</span>
                </div>
            `).join('');
            
            resultsDiv.innerHTML = resultHtml;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载');
            
            // 检查智能助手是否已加载
            setTimeout(() => {
                if (window.aiAssistant) {
                    console.log('智能助手已加载，可以进行实际测试');
                } else {
                    console.log('智能助手未加载，使用模拟测试');
                }
            }, 2000);
        });
    </script>
    
    <!-- 加载智能助手 -->
    <script src="components/load-assistant.js"></script>
</body>
</html>
