<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频推荐功能测试 - 康养平台</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .result-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .video-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        .video-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .video-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        .video-link {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        .video-link:hover {
            text-decoration: underline;
        }
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-test-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .quick-test-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 视频推荐功能测试</h1>
            <p>测试智能视频推荐系统的功能和准确性</p>
        </div>
        
        <div class="test-section">
            <h3>📝 输入测试问题</h3>
            <input type="text" id="testInput" class="test-input" placeholder="请输入您的康复相关问题...">
            <button onclick="testVideoRecommendation()" class="test-button">获取视频推荐</button>
            
            <h4>🚀 快速测试</h4>
            <div class="quick-tests">
                <button class="quick-test-btn" onclick="quickTest('我记忆力不好')">记忆力问题</button>
                <button class="quick-test-btn" onclick="quickTest('说话不清楚')">语言问题</button>
                <button class="quick-test-btn" onclick="quickTest('心情不好')">情绪问题</button>
                <button class="quick-test-btn" onclick="quickTest('手功能训练')">运动康复</button>
                <button class="quick-test-btn" onclick="quickTest('日常生活')">日常生活</button>
                <button class="quick-test-btn" onclick="quickTest('成功案例')">成功案例</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 推荐结果</h3>
            <div id="testResult" class="result-section">
                点击上方按钮开始测试视频推荐功能...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 推荐视频详情</h3>
            <div id="videoDetails"></div>
        </div>
    </div>

    <!-- 加载配置和推荐引擎 -->
    <script src="components/ai-config.js"></script>
    
    <script>
        function testVideoRecommendation() {
            const input = document.getElementById('testInput');
            const resultDiv = document.getElementById('testResult');
            const detailsDiv = document.getElementById('videoDetails');
            
            const userMessage = input.value.trim();
            if (!userMessage) {
                alert('请输入测试问题');
                return;
            }
            
            // 测试视频推荐引擎
            if (window.VideoRecommendationEngine) {
                const response = window.VideoRecommendationEngine.generateVideoResponse(userMessage);
                
                // 显示文本结果
                resultDiv.textContent = response.text;
                
                // 显示视频详情
                if (response.videos && response.videos.length > 0) {
                    let detailsHTML = '';
                    response.videos.forEach((video, index) => {
                        detailsHTML += `
                            <div class="video-card">
                                <div class="video-title">${index + 1}. ${video.title}</div>
                                <div class="video-meta">
                                    ⏱️ ${video.duration} | 🔥 ${video.views}次观看 | 📱 ${video.platform}
                                </div>
                                <p>${video.description}</p>
                                <a href="${video.url}" target="_blank" class="video-link">观看视频 →</a>
                            </div>
                        `;
                    });
                    detailsDiv.innerHTML = detailsHTML;
                } else {
                    detailsDiv.innerHTML = '<p>没有找到相关视频推荐</p>';
                }
            } else {
                resultDiv.textContent = '视频推荐引擎未加载，请刷新页面重试';
            }
        }
        
        function quickTest(question) {
            document.getElementById('testInput').value = question;
            testVideoRecommendation();
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('视频推荐测试页面已加载');
            
            // 检查依赖是否加载
            setTimeout(() => {
                if (window.VideoRecommendationEngine) {
                    console.log('视频推荐引擎已加载');
                } else {
                    console.error('视频推荐引擎未加载');
                }
                
                if (window.AI_KNOWLEDGE_BASE && window.AI_KNOWLEDGE_BASE.videoDatabase) {
                    console.log('视频数据库已加载，包含以下分类：', Object.keys(window.AI_KNOWLEDGE_BASE.videoDatabase));
                } else {
                    console.error('视频数据库未加载');
                }
            }, 500);
        });
    </script>
</body>
</html>
