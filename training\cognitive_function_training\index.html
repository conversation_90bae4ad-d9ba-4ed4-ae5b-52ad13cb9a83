<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认知功能训练 - 康养平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 认知功能训练模块网格 -->
        <div class="cognitive-grid">
            <div class="cognitive-row">
                <!-- 注意力训练 -->
                <div class="cognitive-module" onclick="navigateToTraining('attention')">
                    <div class="module-content">
                        <h2 class="module-title">注意力训练</h2>
                        <p class="module-description">提升专注力和注意力持续时间</p>
                    </div>
                </div>

                <!-- 记忆力训练 -->
                <div class="cognitive-module" onclick="navigateToTraining('memory')">
                    <div class="module-content">
                        <h2 class="module-title">记忆力训练</h2>
                        <p class="module-description">增强短期和长期记忆能力</p>
                    </div>
                </div>

                <!-- 执行能力训练 -->
                <div class="cognitive-module" onclick="navigateToTraining('executive')">
                    <div class="module-content">
                        <h2 class="module-title">执行能力训练</h2>
                        <p class="module-description">提高计划、决策和问题解决能力</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="back-button-container">
            <button class="back-button" onclick="goBack()">返回主页</button>
        </div>
    </div>

    <script>
        function navigateToTraining(type) {
            const routes = {
                'attention': './attention_training/index.html',
                'memory': './memory_training/index.html',
                'executive': './executive_function_training/index.html'
            };
            
            if (routes[type]) {
                window.location.href = routes[type];
            }
        }

        function goBack() {
            window.location.href = '../index.html';
        }
    </script>
</body>
</html>
