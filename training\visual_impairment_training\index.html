<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视觉障碍训练 - 康养平台</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 16px;
            line-height: 1.8;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .training-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #fd7e14;
        }
        .training-item h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .training-item p {
            color: #666;
            margin-bottom: 8px;
        }
        .zoom-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .zoom-btn:hover {
            background: #5a67d8;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 放大缩小控制 -->
    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">
            <i class="fas fa-plus"></i>
        </button>
        <button class="zoom-btn" onclick="zoomOut()">
            <i class="fas fa-minus"></i>
        </button>
        <button class="zoom-btn" onclick="resetZoom()">
            <i class="fas fa-undo"></i>
        </button>
    </div>

    <div class="container">
        <a href="/training/index.html" class="back-link">← 返回训练康复</a>
        
        <div class="header">
            <h1>👁️ 视觉障碍训练</h1>
            <p>改善视觉功能和空间认知</p>
        </div>
        
        <div class="training-item">
            <h3>🔍 视野训练</h3>
            <p>扩展和改善视野范围</p>
            <p>• 视野扩展练习</p>
            <p>• 视野补偿训练</p>
            <p>• 眼球运动练习</p>
            <p>• 视野边界训练</p>
        </div>
        
        <div class="training-item">
            <h3>🎯 视觉搜索训练</h3>
            <p>提升视觉搜索和识别能力</p>
            <p>• 目标搜索练习</p>
            <p>• 特征识别训练</p>
            <p>• 视觉注意训练</p>
            <p>• 视觉跟踪练习</p>
        </div>
        
        <div class="training-item">
            <h3>🗺️ 空间认知训练</h3>
            <p>改善空间感知和定位能力</p>
            <p>• 空间定位练习</p>
            <p>• 空间关系训练</p>
            <p>• 方向感训练</p>
            <p>• 距离判断练习</p>
        </div>
        
        <div class="training-item">
            <h3>⚡ 视觉反应训练</h3>
            <p>提升视觉反应速度和准确性</p>
            <p>• 反应速度训练</p>
            <p>• 视觉辨别练习</p>
            <p>• 动态视觉训练</p>
            <p>• 视觉记忆练习</p>
        </div>
    </div>

    <script>
        let currentZoom = 100;
        
        function zoomIn() {
            currentZoom += 10;
            if (currentZoom > 150) currentZoom = 150;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function zoomOut() {
            currentZoom -= 10;
            if (currentZoom < 80) currentZoom = 80;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function resetZoom() {
            currentZoom = 100;
            document.body.style.zoom = '100%';
        }
    </script>

    <!-- AI智能助手 -->
    <script src="../../components/load-assistant.js"></script>
</body>
</html>
