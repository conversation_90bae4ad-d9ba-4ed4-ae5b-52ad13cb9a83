# 康养平台智能助手

基于Dify的智能助手悬浮窗组件，为康养平台提供智能问答和导航功能。

## 功能特性

- 🤖 **智能对话**: 集成Dify API，提供专业的康复咨询
- 🧭 **智能导航**: 根据用户询问自动生成功能模块链接
- 📱 **响应式设计**: 适配各种设备屏幕尺寸
- 🎨 **美观界面**: 现代化UI设计，支持深色模式
- ⚡ **轻量级**: 模块化设计，按需加载

## 快速开始

### 1. 文件结构

```
components/
├── ai-assistant.html      # 智能助手HTML组件
├── ai-assistant.css       # 样式文件
├── ai-assistant.js        # 核心功能脚本
├── ai-config.js          # 配置文件
├── load-assistant.js     # 加载脚本
└── README.md            # 使用说明
```

### 2. 集成到页面

在需要显示智能助手的页面中添加以下代码：

```html
<!-- 在页面底部，</body>标签前添加 -->
<script src="../components/load-assistant.js"></script>
```

### 3. 配置Dify API

编辑 `components/ai-config.js` 文件：

```javascript
const DIFY_CONFIG = {
    // 您的Dify API Key
    apiKey: 'YOUR_DIFY_API_KEY_HERE',
    
    // Dify API Base URL
    baseUrl: 'https://api.dify.ai/v1',
    
    // 您的Dify应用ID
    appId: 'YOUR_DIFY_APP_ID_HERE'
};
```

## Dify配置指南

### 获取API Key

1. 登录 [Dify控制台](https://dify.ai)
2. 创建新应用或选择现有应用
3. 在应用设置中找到API密钥
4. 复制API Key到配置文件

### 应用配置建议

为了获得最佳效果，建议在Dify中配置以下内容：

#### 系统提示词示例：
```
你是康养平台的智能助手，专门为脑卒中康复患者提供服务。

平台功能包括：
1. 训练康复 - 认知、语言、情绪、视觉训练
2. 量表评估 - 康复进度评估
3. 老人论坛 - 交流社区
4. 短视频频道 - 康复教学
5. 积分系统 - 激励机制

请用温暖、专业的语气回答用户问题，并在适当时候引导用户使用相关功能。
```

#### 知识库内容：
- 康复训练方法和注意事项
- 平台功能使用指南
- 常见康复问题解答
- 康复案例和经验分享

## 自定义配置

### 修改平台功能映射

在 `ai-config.js` 中的 `AI_KNOWLEDGE_BASE.platformFeatures` 部分：

```javascript
platformFeatures: {
    training: {
        name: '训练康复',
        description: '功能描述',
        url: '/training/index.html',
        keywords: ['训练', '康复', '认知']
    }
    // 添加更多功能...
}
```

### 添加常见问题

在 `AI_KNOWLEDGE_BASE.commonQuestions` 中添加：

```javascript
commonQuestions: {
    '新问题': '对应的回答',
    // 更多问题...
}
```

### 自定义样式

修改 `ai-assistant.css` 文件中的CSS变量：

```css
:root {
    --ai-primary-color: #667eea;
    --ai-secondary-color: #764ba2;
    --ai-text-color: #333;
    --ai-bg-color: #ffffff;
}
```

## API接口

### 主要方法

```javascript
// 获取AI助手实例
const assistant = window.aiAssistant;

// 打开聊天窗口
assistant.openChat();

// 关闭聊天窗口
assistant.closeChat();

// 发送消息
assistant.addMessage('消息内容', 'user');

// 导航到模块
assistant.navigateToModule('training');

// 配置Dify API
assistant.configureDify(apiKey, baseUrl, appId);
```

## 故障排除

### 常见问题

1. **智能助手不显示**
   - 检查 `load-assistant.js` 路径是否正确
   - 确认所有组件文件都已上传
   - 查看浏览器控制台是否有错误

2. **Dify API调用失败**
   - 验证API Key是否正确
   - 检查网络连接
   - 确认Dify应用状态正常

3. **样式显示异常**
   - 检查CSS文件是否正确加载
   - 确认没有样式冲突
   - 清除浏览器缓存

### 调试模式

在浏览器控制台中启用调试：

```javascript
// 查看配置
console.log(window.DIFY_CONFIG);

// 查看AI助手实例
console.log(window.aiAssistant);

// 测试API连接
window.aiAssistant.callDifyAPI('测试消息');
```

## 更新日志

### v1.0.0
- 初始版本发布
- 基础智能对话功能
- 响应式设计
- Dify API集成

## 技术支持

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证。
