// 训练康复模块导航功能

// 导航到具体的训练模块
function navigateToModule(moduleType) {
    let targetPath = '';
    
    switch(moduleType) {
        case 'cognitive':
            targetPath = './cognitive_function_training/index.html';
            break;
        case 'language':
            targetPath = './language_function_training/index.html';
            break;
        case 'visual':
            targetPath = './visual_impairment_training/index.html';
            break;
        case 'emotional':
            targetPath = './emotional_regulation_training/index.html';
            break;
        default:
            console.log('未知的模块类型:', moduleType);
            return;
    }
    
    // 检查目标页面是否存在，如果不存在则显示提示
    fetch(targetPath, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                window.location.href = targetPath;
            } else {
                showModuleNotReady(getModuleName(moduleType));
            }
        })
        .catch(() => {
            showModuleNotReady(getModuleName(moduleType));
        });
}

// 获取模块中文名称
function getModuleName(moduleType) {
    const moduleNames = {
        'cognitive': '认知功能训练',
        'language': '语言功能训练',
        'visual': '视觉障碍训练',
        'emotional': '情绪调节训练'
    };
    return moduleNames[moduleType] || '未知模块';
}

// 显示模块未准备好的提示
function showModuleNotReady(moduleName) {
    alert(`${moduleName}模块正在开发中，敬请期待！`);
}

// 返回主页
function goBack() {
    // 检查是否有上一页历史记录
    if (window.history.length > 1) {
        window.history.back();
    } else {
        // 如果没有历史记录，导航到主页
        window.location.href = '../home/<USER>';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加键盘导航支持
    document.addEventListener('keydown', function(event) {
        // ESC键返回
        if (event.key === 'Escape') {
            goBack();
        }
        
        // 数字键快速导航
        switch(event.key) {
            case '1':
                navigateToModule('cognitive');
                break;
            case '2':
                navigateToModule('language');
                break;
            case '3':
                navigateToModule('visual');
                break;
            case '4':
                navigateToModule('emotional');
                break;
        }
    });
    
    // 添加模块悬停效果的声音反馈（可选）
    const modules = document.querySelectorAll('.training-module');
    modules.forEach(module => {
        module.addEventListener('mouseenter', function() {
            // 可以在这里添加声音效果或其他反馈
            this.style.transform = 'translateY(-8px)';
        });
        
        module.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    console.log('训练康复页面已加载完成');
});
