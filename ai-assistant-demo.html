<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手演示 - 康养平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-instructions {
            background: #e6f7ff;
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 30px;
        }
        
        .instructions-title {
            font-size: 1.5rem;
            color: #1890ff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .instructions-list {
            list-style: none;
            padding: 0;
        }
        
        .instructions-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(24, 144, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .instructions-list li:last-child {
            border-bottom: none;
        }
        
        .step-number {
            background: #1890ff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .demo-config {
            background: #fff7e6;
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 30px;
        }
        
        .config-title {
            font-size: 1.5rem;
            color: #fa8c16;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-code {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .demo-footer {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #eee;
            color: #666;
        }
        
        .assistant-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            font-size: 14px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            animation: bounce 2s infinite;
            pointer-events: none;
            z-index: 9998;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }
            
            .demo-title {
                font-size: 2rem;
            }
            
            .demo-features {
                grid-template-columns: 1fr;
            }
            
            .assistant-indicator {
                bottom: 100px;
                right: 20px;
                font-size: 12px;
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🤖 智能助手演示</h1>
            <p class="demo-subtitle">基于Dify的康养平台智能助手</p>
        </div>
        
        <div class="demo-features">
            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3 class="feature-title">智能对话</h3>
                <p class="feature-description">集成Dify API，提供专业的康复咨询和平台功能介绍</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🧭</div>
                <h3 class="feature-title">智能导航</h3>
                <p class="feature-description">根据用户询问自动生成功能模块链接，快速跳转</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">响应式设计</h3>
                <p class="feature-description">适配各种设备屏幕尺寸，提供一致的用户体验</p>
            </div>
        </div>
        
        <div class="demo-instructions">
            <h2 class="instructions-title">
                <span>📋</span>
                使用说明
            </h2>
            <ul class="instructions-list">
                <li>
                    <span class="step-number">1</span>
                    <span>点击右下角的智能助手悬浮按钮</span>
                </li>
                <li>
                    <span class="step-number">2</span>
                    <span>在聊天窗口中输入您的问题</span>
                </li>
                <li>
                    <span class="step-number">3</span>
                    <span>尝试询问"训练康复"、"量表评估"等功能</span>
                </li>
                <li>
                    <span class="step-number">4</span>
                    <span>点击生成的导航链接快速跳转</span>
                </li>
                <li>
                    <span class="step-number">5</span>
                    <span>使用快捷按钮快速访问常用功能</span>
                </li>
            </ul>
        </div>
        
        <div class="demo-config">
            <h2 class="config-title">
                <span>⚙️</span>
                配置Dify API
            </h2>
            <p>要启用真实的AI对话功能，请编辑 <code>components/ai-config.js</code> 文件：</p>
            <div class="config-code">
const DIFY_CONFIG = {
    apiKey: 'YOUR_DIFY_API_KEY_HERE',
    baseUrl: 'https://api.dify.ai/v1',
    appId: 'YOUR_DIFY_APP_ID_HERE'
};
            </div>
            <p>获取API Key：登录 <a href="https://dify.ai" target="_blank">Dify控制台</a> → 创建应用 → 获取API密钥</p>
        </div>
        
        <div class="demo-footer">
            <p>💡 当前使用模拟回复演示功能，配置Dify API后可获得真实的AI对话体验</p>
        </div>
    </div>
    
    <!-- 智能助手指示器 -->
    <div class="assistant-indicator">
        👆 点击智能助手试试看！
    </div>
    
    <!-- 加载智能助手 -->
    <script src="components/load-assistant.js"></script>
</body>
</html>
