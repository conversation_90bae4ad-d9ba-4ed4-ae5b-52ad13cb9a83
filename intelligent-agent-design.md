# 康养平台智能体设计方案

## 🎯 智能体核心功能

### 1. 问题理解与意图识别
- **症状映射**: 将用户描述的症状映射到具体康复训练
- **需求分析**: 理解用户的康复目标和当前状态
- **情感识别**: 识别用户的情绪状态，提供温暖支持

### 2. 智能导航系统
- **精准链接生成**: 根据问题自动生成对应功能模块链接
- **多层级导航**: 支持主功能→子功能的层级导航
- **个性化推荐**: 基于用户历史和偏好推荐相关功能

### 3. 工作流指导引擎
- **分步骤指导**: 将复杂任务分解为简单步骤
- **实时验证**: 每步完成后进行验证确认
- **错误处理**: 智能识别和处理用户操作错误

## 🧠 智能体知识体系

### 康复训练知识库
```
认知功能训练:
  - 注意力训练: 集中注意力、分散注意力、持续注意力
  - 记忆力训练: 工作记忆、长期记忆、情景记忆
  - 执行功能: 计划能力、决策能力、问题解决

语言功能训练:
  - 理解训练: 词汇理解、句子理解、语篇理解
  - 表达训练: 命名、复述、描述、对话
  - 读写训练: 阅读理解、书写练习

视觉障碍训练:
  - 视野训练: 视野扩展、视野补偿
  - 视觉搜索: 目标搜索、特征识别
  - 空间认知: 空间定位、空间关系

情绪调节训练:
  - 情绪识别: 面部表情、情绪词汇
  - 放松训练: 深呼吸、渐进性肌肉放松
  - 认知重构: 负性思维识别、积极思维培养
```

### 症状-功能映射表
```
记忆问题 → /training/cognitive_function_training/memory_training/
注意力问题 → /training/cognitive_function_training/attention_training/
语言困难 → /training/language_function_training/
视觉问题 → /training/visual_impairment_training/
情绪低落 → /training/emotional_regulation_training/
想评估进度 → /assessment/
想交流经验 → /forum/
学习康复知识 → /videos/
获得激励 → /points/
```

## 🔄 工作流设计

### 标准康复工作流
1. **初始评估** → 量表评估模块
2. **制定计划** → 根据评估结果推荐训练
3. **执行训练** → 相应训练模块
4. **进度跟踪** → 积分系统 + 再次评估
5. **社交支持** → 论坛交流
6. **知识学习** → 视频频道

### 应急处理工作流
1. **情绪危机** → 立即推荐情绪调节训练
2. **功能退化** → 紧急评估 + 强化训练
3. **技术问题** → 分步指导解决

## 🎨 用户交互设计

### 对话模式
- **温暖开场**: "您好！我是您的康复小助手..."
- **耐心倾听**: 充分理解用户描述
- **专业建议**: 基于医学知识提供建议
- **鼓励支持**: 给予积极的心理支持

### 响应格式
```
🎯 **推荐功能**: [功能名称](链接)
📋 **具体内容**: 
   - [子功能1](链接) - 说明
   - [子功能2](链接) - 说明
💡 **使用建议**: 具体指导
🔄 **下一步**: 后续建议
```

## 🛠️ 技术实现架构

### 前端组件
- **智能助手悬浮窗**: 已有组件优化
- **导航链接生成器**: 动态生成可点击链接
- **进度跟踪器**: 记录用户操作路径

### 后端服务
- **Dify API集成**: 智能对话引擎
- **知识库管理**: 康复知识存储
- **用户行为分析**: 个性化推荐

### 数据流
用户输入 → Dify处理 → 意图识别 → 链接生成 → 页面跳转 → 行为记录

## 📊 效果评估指标

### 功能性指标
- 问题理解准确率 > 90%
- 链接跳转成功率 > 95%
- 用户满意度 > 4.5/5

### 用户体验指标
- 平均响应时间 < 2秒
- 对话完成率 > 80%
- 功能使用率提升 > 30%

## 🚀 实施计划

### 第一阶段: 基础功能
- 优化Dify配置
- 完善知识库
- 测试基本对话

### 第二阶段: 智能导航
- 实现链接自动生成
- 测试页面跳转
- 优化用户体验

### 第三阶段: 工作流指导
- 设计指导流程
- 实现步骤验证
- 完善错误处理

### 第四阶段: 优化完善
- 收集用户反馈
- 持续优化算法
- 扩展功能边界
