/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
    color: #2c3e50;
    line-height: 1.6;
    min-height: 100vh;
    font-size: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 50px 30px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 50px;
}



/* 训练模块网格布局 */
.training-grid {
    display: flex;
    flex-direction: column;
    gap: 40px;
    flex: 1;
    margin-top: 80px;
    justify-content: center;
}

.training-row {
    display: flex;
    gap: 40px;
    height: 350px;
}

.training-module {
    flex: 1;
    background: linear-gradient(135deg, #87ceeb, #add8e6);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(135, 206, 235, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.training-module:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(135, 206, 235, 0.4);
    background: linear-gradient(135deg, #7ec8e3, #9fd3e8);
}

.training-module:active {
    transform: translateY(-4px);
}

.module-content {
    text-align: center;
    padding: 30px;
    color: #ffffff;
}

.module-title {
    font-size: 2.6rem;
    font-weight: 600;
    margin-bottom: 18px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.module-description {
    font-size: 1.4rem;
    font-weight: 400;
    opacity: 0.9;
    line-height: 1.5;
}

/* 返回按钮 */
.back-button-container {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.back-button {
    background: linear-gradient(135deg, #87ceeb, #add8e6);
    color: #ffffff;
    border: none;
    padding: 18px 50px;
    font-size: 1.4rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(135, 206, 235, 0.3);
}

.back-button:hover {
    background: linear-gradient(135deg, #7ec8e3, #9fd3e8);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(135, 206, 235, 0.4);
}

.back-button:active {
    transform: translateY(0);
}

/* 内容区域样式 */
.content-area {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(135, 206, 235, 0.2);
}

.module-info h2 {
    font-size: 2rem;
    color: #4682b4;
    margin-bottom: 20px;
    font-weight: 600;
}

.module-info p {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 20px;
    line-height: 1.6;
}

.module-info ul {
    list-style: none;
    padding: 0;
}

.module-info li {
    font-size: 1.2rem;
    color: #4682b4;
    padding: 10px 0;
    border-bottom: 1px solid rgba(135, 206, 235, 0.3);
    position: relative;
    padding-left: 30px;
}

.module-info li:before {
    content: "•";
    color: #87ceeb;
    font-size: 1.5rem;
    position: absolute;
    left: 0;
    top: 5px;
}

.module-info li:last-child {
    border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .training-row {
        flex-direction: column;
        height: auto;
        gap: 20px;
    }
    
    .training-module {
        height: 250px;
    }



    .module-title {
        font-size: 2.2rem;
    }

    .module-description {
        font-size: 1.2rem;
    }
    
    .container {
        padding: 20px 15px;
    }
}

@media (max-width: 480px) {

    
    .module-title {
        font-size: 1.8rem;
    }

    .module-description {
        font-size: 1.1rem;
    }
}
