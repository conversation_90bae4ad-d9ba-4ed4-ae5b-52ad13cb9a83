/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
    color: #2c3e50;
    line-height: 1.6;
    min-height: 100vh;
    font-size: 20px;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 60px 40px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 60px;
}

/* 认知功能训练模块网格布局 */
.cognitive-grid {
    display: flex;
    flex-direction: column;
    gap: 50px;
    flex: 1;
    margin-top: 100px;
    justify-content: center;
}

.cognitive-row {
    display: flex;
    gap: 50px;
    height: 420px;
}

.cognitive-module {
    flex: 1;
    background: linear-gradient(135deg, #87ceeb, #add8e6);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(135, 206, 235, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.cognitive-module:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(135, 206, 235, 0.4);
    background: linear-gradient(135deg, #7ec8e3, #9fd3e8);
}

.cognitive-module:active {
    transform: translateY(-4px);
}

.module-content {
    text-align: center;
    padding: 40px;
    color: #ffffff;
}

.module-title {
    font-size: 3.0rem;
    font-weight: 600;
    margin-bottom: 22px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.module-description {
    font-size: 1.6rem;
    font-weight: 400;
    opacity: 0.9;
    line-height: 1.5;
}

/* 返回按钮 */
.back-button-container {
    display: flex;
    justify-content: center;
    margin-top: 50px;
}

.back-button {
    background: linear-gradient(135deg, #87ceeb, #add8e6);
    color: #ffffff;
    border: none;
    padding: 22px 60px;
    font-size: 1.6rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(135, 206, 235, 0.3);
}

.back-button:hover {
    background: linear-gradient(135deg, #7ec8e3, #9fd3e8);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(135, 206, 235, 0.4);
}

.back-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cognitive-row {
        flex-direction: column;
        height: auto;
        gap: 30px;
    }

    .cognitive-module {
        height: 300px;
    }

    .module-title {
        font-size: 2.6rem;
    }

    .module-description {
        font-size: 1.4rem;
    }

    .container {
        padding: 30px 20px;
    }
}

@media (max-width: 480px) {
    .module-title {
        font-size: 2.2rem;
    }

    .module-description {
        font-size: 1.3rem;
    }

    .cognitive-module {
        height: 250px;
    }

    .back-button {
        padding: 18px 50px;
        font-size: 1.4rem;
    }
}
