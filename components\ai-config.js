/**
 * AI助手配置文件
 * 在这里配置您的Dify API信息
 */

// Dify API配置 - 本地部署版本
window.DIFY_CONFIG = window.DIFY_CONFIG || {
    // 您的Dify API Key - 已配置
    apiKey: 'app-mkGNIAGwOq1qcW2toI6IruXZ',

    // Dify API Base URL - 本地部署地址
    baseUrl: 'http://127.0.0.1/v1',

    // 您的Dify应用ID/Token
    appId: 'vSE7UiS3QeR76ycy',

    // Dify嵌入配置（用于官方嵌入方式）
    embedConfig: {
        token: 'vSE7UiS3QeR76ycy',
        baseUrl: 'https://api.dify.ai',  // 修正为官方地址
        systemVariables: {
            platform: 'kangyang-platform',
            version: '1.0.0'
        },
        userVariables: {
            user_type: 'patient_or_family'
        }
    },

    // 对话用户标识
    user: 'kangyang-platform-user'
};

// 智能助手知识库配置
const AI_KNOWLEDGE_BASE = {
    // 平台功能介绍
    platformFeatures: {
        training: {
            name: '训练康复',
            description: '提供认知功能训练、语言功能训练、情绪调节训练和视觉障碍训练等多种康复训练项目',
            url: '/training/index.html',
            keywords: ['训练', '康复', '认知', '语言', '情绪', '视觉']
        },
        assessment: {
            name: '量表评估',
            description: '科学评估康复进度，制定个性化康复方案，跟踪康复效果',
            url: '/assessment/index.html',
            keywords: ['评估', '量表', '进度', '方案', '测试']
        },
        forum: {
            name: '老人论坛',
            description: '温馨的交流社区，分享康复经验，互相鼓励支持，专家在线答疑',
            url: '/forum/index.html',
            keywords: ['论坛', '交流', '社区', '分享', '讨论']
        },
        videos: {
            name: '短视频频道',
            description: '康复教学视频、专家讲座、康复案例分享、健康生活指导',
            url: '/videos/index.html',
            keywords: ['视频', '教学', '讲座', '案例', '学习']
        },
        points: {
            name: '积分系统',
            description: '完成训练获得积分，参与论坛讨论得分，积分兑换精美礼品',
            url: '/points/index.html',
            keywords: ['积分', '奖励', '兑换', '礼品', '激励']
        }
    },
    
    // 常见问题回答
    commonQuestions: {
        '如何开始康复训练': '您可以点击"训练康复"模块，选择适合您的训练项目。建议先进行量表评估，了解自己的康复状况，然后制定个性化的训练计划。',
        '平台有哪些功能': '康养平台包含五大核心功能：训练康复、量表评估、老人论坛、短视频频道和积分系统。每个功能都专为脑卒中康复设计。',
        '如何获得积分': '您可以通过完成康复训练、参与论坛讨论、观看教学视频等方式获得积分。积分可以用来兑换精美礼品。',
        '论坛如何使用': '在老人论坛中，您可以发布帖子分享康复经验，回复其他用户的问题，参与专家答疑活动。这是一个温馨的康复交流社区。'
    },
    
    // 康复知识库
    rehabilitationKnowledge: {
        '认知功能训练': '认知功能训练主要包括记忆力训练、注意力训练、执行功能训练等，通过科学的训练方法帮助改善认知能力。',
        '语言功能训练': '语言功能训练针对失语症患者，包括理解训练、表达训练、阅读训练和书写训练，循序渐进地恢复语言能力。',
        '情绪调节训练': '情绪调节训练帮助患者管理焦虑、抑郁等负面情绪，通过放松训练、认知重构等方法改善心理状态。',
        '视觉障碍训练': '视觉障碍训练针对视野缺损、视觉忽略等问题，通过专业的视觉康复训练改善视觉功能。'
    },

    // 症状关键词映射 - 智能识别用户需求
    symptomMapping: {
        // 认知相关症状
        '记忆': { module: 'training', subModule: 'cognitive', keywords: ['记忆力', '忘记', '健忘'] },
        '注意力': { module: 'training', subModule: 'cognitive', keywords: ['专注', '集中', '分心'] },
        '思维': { module: 'training', subModule: 'cognitive', keywords: ['反应', '理解', '计算'] },

        // 语言相关症状
        '说话': { module: 'training', subModule: 'language', keywords: ['表达', '发音', '沟通'] },
        '语言': { module: 'training', subModule: 'language', keywords: ['交流', '词汇', '理解'] },

        // 视觉相关症状
        '视力': { module: 'training', subModule: 'visual', keywords: ['看', '视觉', '模糊'] },
        '视野': { module: 'training', subModule: 'visual', keywords: ['看不清', '眼睛'] },

        // 情绪相关症状
        '情绪': { module: 'training', subModule: 'emotional', keywords: ['心情', '抑郁', '焦虑'] },
        '心理': { module: 'training', subModule: 'emotional', keywords: ['压力', '紧张', '烦躁'] }
    },

    // 智能回复模板
    responseTemplates: {
        linkFormat: "🎯 **[{name}]({url})**\n\n{description}\n\n💡 **使用建议：**\n{suggestion}",
        encouragement: [
            "康复是一个循序渐进的过程，请保持耐心和信心！💪",
            "每一次训练都是向康复迈进的一步，加油！🌟",
            "您的努力一定会有回报，坚持就是胜利！🎯"
        ],
        greeting: [
            "您好！我是康养平台的智能助手，很高兴为您服务！🤖",
            "欢迎来到康养平台！我是您的专属康复小助手 😊",
            "您好！我可以帮您找到合适的康复训练和服务 🌟"
        ]
    }
};

// 智能回复生成器
class AIResponseGenerator {
    static generateResponse(userMessage) {
        const message = userMessage.toLowerCase();
        
        // 检查是否询问特定功能
        for (const [key, feature] of Object.entries(AI_KNOWLEDGE_BASE.platformFeatures)) {
            if (feature.keywords.some(keyword => message.includes(keyword))) {
                return {
                    text: `${feature.description}\n\n您想了解更多详情吗？我可以为您提供直接链接。`,
                    module: key,
                    moduleName: feature.name
                };
            }
        }
        
        // 检查常见问题
        for (const [question, answer] of Object.entries(AI_KNOWLEDGE_BASE.commonQuestions)) {
            if (message.includes(question.toLowerCase()) || 
                question.toLowerCase().includes(message)) {
                return {
                    text: answer,
                    module: null,
                    moduleName: null
                };
            }
        }
        
        // 检查康复知识
        for (const [topic, knowledge] of Object.entries(AI_KNOWLEDGE_BASE.rehabilitationKnowledge)) {
            if (message.includes(topic.toLowerCase())) {
                return {
                    text: knowledge,
                    module: 'training',
                    moduleName: '训练康复'
                };
            }
        }
        
        // 默认回复
        return {
            text: '您好！我是康养平台智能助手。我可以帮您：\n\n• 了解平台各项功能\n• 快速导航到相关模块\n• 解答康复相关问题\n• 提供使用指导\n\n请告诉我您想了解什么？',
            module: null,
            moduleName: null
        };
    }
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DIFY_CONFIG, AI_KNOWLEDGE_BASE, AIResponseGenerator };
} else {
    window.DIFY_CONFIG = DIFY_CONFIG;
    window.AI_KNOWLEDGE_BASE = AI_KNOWLEDGE_BASE;
    window.AIResponseGenerator = AIResponseGenerator;
}
