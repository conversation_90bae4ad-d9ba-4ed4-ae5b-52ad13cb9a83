/**
 * AI助手配置文件
 * 在这里配置您的Dify API信息
 */

// Dify API配置 - 本地部署版本
window.DIFY_CONFIG = window.DIFY_CONFIG || {
    // 您的Dify API Key - 已配置
    apiKey: 'app-mkGNIAGwOq1qcW2toI6IruXZ',

    // Dify API Base URL - 本地部署地址
    baseUrl: 'http://127.0.0.1/v1',

    // 您的Dify应用ID/Token
    appId: 'vSE7UiS3QeR76ycy',

    // Dify嵌入配置（用于官方嵌入方式）
    embedConfig: {
        token: 'vSE7UiS3QeR76ycy',
        baseUrl: 'https://api.dify.ai',  // 修正为官方地址
        systemVariables: {
            platform: 'kangyang-platform',
            version: '1.0.0'
        },
        userVariables: {
            user_type: 'patient_or_family'
        }
    },

    // 对话用户标识
    user: 'kangyang-platform-user'
};

// 智能助手知识库配置
const AI_KNOWLEDGE_BASE = {
    // 平台功能介绍
    platformFeatures: {
        training: {
            name: '训练康复',
            description: '提供认知功能训练、语言功能训练、情绪调节训练和视觉障碍训练等多种康复训练项目',
            url: '/training/index.html',
            keywords: ['训练', '康复', '认知', '语言', '情绪', '视觉']
        },
        assessment: {
            name: '量表评估',
            description: '科学评估康复进度，制定个性化康复方案，跟踪康复效果',
            url: '/assessment/index.html',
            keywords: ['评估', '量表', '进度', '方案', '测试']
        },
        forum: {
            name: '老人论坛',
            description: '温馨的交流社区，分享康复经验，互相鼓励支持，专家在线答疑',
            url: '/forum/index.html',
            keywords: ['论坛', '交流', '社区', '分享', '讨论']
        },
        videos: {
            name: '短视频频道',
            description: '康复教学视频、专家讲座、康复案例分享、健康生活指导，智能推荐相关康复视频',
            url: '/videos/index.html',
            keywords: ['视频', '教学', '讲座', '案例', '学习', '推荐']
        },
        points: {
            name: '积分系统',
            description: '完成训练获得积分，参与论坛讨论得分，积分兑换精美礼品',
            url: '/points/index.html',
            keywords: ['积分', '奖励', '兑换', '礼品', '激励']
        }
    },

    // 康复视频数据库
    videoDatabase: {
        cognitive: [
            {
                id: 'cog_001',
                title: '记忆力训练基础教程',
                description: '专业康复师指导的记忆力训练方法，包括数字记忆、图像记忆等技巧',
                url: 'https://v.douyin.com/ieFqMjNh/',
                platform: '抖音',
                category: '认知功能',
                keywords: ['记忆力', '记忆', '认知', '训练', '大脑'],
                duration: '5分钟',
                difficulty: '初级',
                views: '12.3万'
            },
            {
                id: 'cog_002',
                title: '注意力集中训练法',
                description: '通过简单的日常练习提升注意力集中度，适合脑卒中康复患者',
                url: 'https://v.douyin.com/ieFqRjKl/',
                platform: '抖音',
                category: '认知功能',
                keywords: ['注意力', '专注', '集中', '训练', '康复'],
                duration: '3分钟',
                difficulty: '初级',
                views: '8.7万'
            },
            {
                id: 'cog_003',
                title: '执行功能康复指导',
                description: '康复专家讲解执行功能训练要点，帮助患者恢复计划和决策能力',
                url: 'https://v.douyin.com/ieFqTnPx/',
                platform: '抖音',
                category: '认知功能',
                keywords: ['执行功能', '计划', '决策', '思维', '康复'],
                duration: '7分钟',
                difficulty: '中级',
                views: '6.2万'
            }
        ],
        language: [
            {
                id: 'lang_001',
                title: '失语症康复训练方法',
                description: '语言治疗师演示失语症患者的语言康复训练技巧和日常练习',
                url: 'https://v.douyin.com/ieFqYmBv/',
                platform: '抖音',
                category: '语言功能',
                keywords: ['失语症', '语言', '说话', '表达', '康复'],
                duration: '8分钟',
                difficulty: '中级',
                views: '15.6万'
            },
            {
                id: 'lang_002',
                title: '发音练习基础教程',
                description: '专业发音训练方法，帮助改善言语清晰度和流畅性',
                url: 'https://v.douyin.com/ieFqUkLm/',
                platform: '抖音',
                category: '语言功能',
                keywords: ['发音', '言语', '清晰', '流畅', '练习'],
                duration: '6分钟',
                difficulty: '初级',
                views: '9.8万'
            },
            {
                id: 'lang_003',
                title: '阅读理解康复训练',
                description: '循序渐进的阅读理解训练方法，提升语言理解能力',
                url: 'https://v.douyin.com/ieFqWnCx/',
                platform: '抖音',
                category: '语言功能',
                keywords: ['阅读', '理解', '语言', '文字', '训练'],
                duration: '5分钟',
                difficulty: '初级',
                views: '7.4万'
            }
        ],
        emotional: [
            {
                id: 'emo_001',
                title: '康复期情绪调节技巧',
                description: '心理专家分享康复期间的情绪管理方法，缓解焦虑和抑郁',
                url: 'https://v.douyin.com/ieFqZpDy/',
                platform: '抖音',
                category: '情绪调节',
                keywords: ['情绪', '心理', '焦虑', '抑郁', '调节'],
                duration: '10分钟',
                difficulty: '初级',
                views: '18.2万'
            },
            {
                id: 'emo_002',
                title: '放松训练指导',
                description: '深呼吸和肌肉放松训练，帮助缓解康复过程中的紧张情绪',
                url: 'https://v.douyin.com/ieFqApBz/',
                platform: '抖音',
                category: '情绪调节',
                keywords: ['放松', '呼吸', '肌肉', '紧张', '缓解'],
                duration: '12分钟',
                difficulty: '初级',
                views: '11.5万'
            },
            {
                id: 'emo_003',
                title: '积极心态建立方法',
                description: '康复心理学专家教授如何在康复过程中保持积极乐观的心态',
                url: 'https://v.douyin.com/ieFqBqCa/',
                platform: '抖音',
                category: '情绪调节',
                keywords: ['积极', '心态', '乐观', '信心', '康复'],
                duration: '9分钟',
                difficulty: '初级',
                views: '13.7万'
            }
        ],
        physical: [
            {
                id: 'phy_001',
                title: '床上康复训练动作',
                description: '适合卧床患者的康复训练动作，预防肌肉萎缩和关节僵硬',
                url: 'https://v.douyin.com/ieFqCrDb/',
                platform: '抖音',
                category: '运动康复',
                keywords: ['床上', '运动', '肌肉', '关节', '训练'],
                duration: '15分钟',
                difficulty: '初级',
                views: '22.1万'
            },
            {
                id: 'phy_002',
                title: '平衡训练基础教程',
                description: '康复师指导的平衡能力训练，提升站立和行走稳定性',
                url: 'https://v.douyin.com/ieFqDsEc/',
                platform: '抖音',
                category: '运动康复',
                keywords: ['平衡', '站立', '行走', '稳定', '训练'],
                duration: '8分钟',
                difficulty: '中级',
                views: '16.8万'
            },
            {
                id: 'phy_003',
                title: '手功能康复训练',
                description: '精细动作训练方法，帮助恢复手部功能和灵活性',
                url: 'https://v.douyin.com/ieFqEtFd/',
                platform: '抖音',
                category: '运动康复',
                keywords: ['手功能', '精细动作', '灵活', '康复', '训练'],
                duration: '6分钟',
                difficulty: '初级',
                views: '14.3万'
            }
        ],
        daily: [
            {
                id: 'daily_001',
                title: '日常生活能力训练',
                description: '康复师演示日常生活技能训练，包括穿衣、洗漱、进食等',
                url: 'https://v.douyin.com/ieFqFuGe/',
                platform: '抖音',
                category: '日常生活',
                keywords: ['日常', '生活', '技能', '独立', '训练'],
                duration: '12分钟',
                difficulty: '中级',
                views: '19.6万'
            },
            {
                id: 'daily_002',
                title: '家庭康复环境布置',
                description: '专家指导如何布置安全舒适的家庭康复环境',
                url: 'https://v.douyin.com/ieFqGvHf/',
                platform: '抖音',
                category: '日常生活',
                keywords: ['家庭', '环境', '安全', '布置', '康复'],
                duration: '7分钟',
                difficulty: '初级',
                views: '10.2万'
            }
        ],
        success_stories: [
            {
                id: 'story_001',
                title: '康复成功案例分享',
                description: '真实的脑卒中康复成功故事，给患者和家属带来希望和信心',
                url: 'https://v.douyin.com/ieFqHwIg/',
                platform: '抖音',
                category: '成功案例',
                keywords: ['成功', '案例', '故事', '希望', '信心'],
                duration: '10分钟',
                difficulty: '初级',
                views: '25.4万'
            },
            {
                id: 'story_002',
                title: '康复路上的坚持',
                description: '患者分享康复过程中的心路历程和坚持的力量',
                url: 'https://v.douyin.com/ieFqJxKh/',
                platform: '抖音',
                category: '成功案例',
                keywords: ['坚持', '心路', '历程', '力量', '康复'],
                duration: '8分钟',
                difficulty: '初级',
                views: '17.9万'
            }
        ]
    },
    
    // 常见问题回答
    commonQuestions: {
        '如何开始康复训练': '您可以点击"训练康复"模块，选择适合您的训练项目。建议先进行量表评估，了解自己的康复状况，然后制定个性化的训练计划。',
        '平台有哪些功能': '康养平台包含五大核心功能：训练康复、量表评估、老人论坛、短视频频道和积分系统。每个功能都专为脑卒中康复设计。',
        '如何获得积分': '您可以通过完成康复训练、参与论坛讨论、观看教学视频等方式获得积分。积分可以用来兑换精美礼品。',
        '论坛如何使用': '在老人论坛中，您可以发布帖子分享康复经验，回复其他用户的问题，参与专家答疑活动。这是一个温馨的康复交流社区。'
    },
    
    // 康复知识库
    rehabilitationKnowledge: {
        '认知功能训练': '认知功能训练主要包括记忆力训练、注意力训练、执行功能训练等，通过科学的训练方法帮助改善认知能力。',
        '语言功能训练': '语言功能训练针对失语症患者，包括理解训练、表达训练、阅读训练和书写训练，循序渐进地恢复语言能力。',
        '情绪调节训练': '情绪调节训练帮助患者管理焦虑、抑郁等负面情绪，通过放松训练、认知重构等方法改善心理状态。',
        '视觉障碍训练': '视觉障碍训练针对视野缺损、视觉忽略等问题，通过专业的视觉康复训练改善视觉功能。'
    },

    // 症状关键词映射 - 智能识别用户需求
    symptomMapping: {
        // 认知相关症状
        '记忆': { module: 'training', subModule: 'cognitive', keywords: ['记忆力', '忘记', '健忘'] },
        '注意力': { module: 'training', subModule: 'cognitive', keywords: ['专注', '集中', '分心'] },
        '思维': { module: 'training', subModule: 'cognitive', keywords: ['反应', '理解', '计算'] },

        // 语言相关症状
        '说话': { module: 'training', subModule: 'language', keywords: ['表达', '发音', '沟通'] },
        '语言': { module: 'training', subModule: 'language', keywords: ['交流', '词汇', '理解'] },

        // 视觉相关症状
        '视力': { module: 'training', subModule: 'visual', keywords: ['看', '视觉', '模糊'] },
        '视野': { module: 'training', subModule: 'visual', keywords: ['看不清', '眼睛'] },

        // 情绪相关症状
        '情绪': { module: 'training', subModule: 'emotional', keywords: ['心情', '抑郁', '焦虑'] },
        '心理': { module: 'training', subModule: 'emotional', keywords: ['压力', '紧张', '烦躁'] }
    },

    // 智能回复模板
    responseTemplates: {
        linkFormat: "🎯 **[{name}]({url})**\n\n{description}\n\n💡 **使用建议：**\n{suggestion}",
        videoFormat: "🎬 **{title}**\n📝 {description}\n⏱️ 时长：{duration} | 🔥 {views}次观看\n🔗 [{platform}链接]({url})",
        encouragement: [
            "康复是一个循序渐进的过程，请保持耐心和信心！💪",
            "每一次训练都是向康复迈进的一步，加油！🌟",
            "您的努力一定会有回报，坚持就是胜利！🎯"
        ],
        greeting: [
            "您好！我是康养平台的智能助手，很高兴为您服务！🤖",
            "欢迎来到康养平台！我是您的专属康复小助手 😊",
            "您好！我可以帮您找到合适的康复训练和服务 🌟"
        ]
    },

    // 视频推荐关键词映射
    videoKeywordMapping: {
        // 认知功能相关
        '记忆': ['cognitive'],
        '记忆力': ['cognitive'],
        '忘记': ['cognitive'],
        '想不起': ['cognitive'],
        '注意力': ['cognitive'],
        '专注': ['cognitive'],
        '集中': ['cognitive'],
        '思维': ['cognitive'],
        '认知': ['cognitive'],
        '大脑': ['cognitive'],
        '智力': ['cognitive'],

        // 语言功能相关
        '说话': ['language'],
        '语言': ['language'],
        '表达': ['language'],
        '失语': ['language'],
        '发音': ['language'],
        '言语': ['language'],
        '阅读': ['language'],
        '理解': ['language'],
        '沟通': ['language'],
        '交流': ['language'],

        // 情绪调节相关
        '情绪': ['emotional'],
        '心情': ['emotional'],
        '抑郁': ['emotional'],
        '焦虑': ['emotional'],
        '紧张': ['emotional'],
        '压力': ['emotional'],
        '心理': ['emotional'],
        '放松': ['emotional'],
        '积极': ['emotional'],
        '乐观': ['emotional'],

        // 运动康复相关
        '运动': ['physical'],
        '锻炼': ['physical'],
        '肌肉': ['physical'],
        '关节': ['physical'],
        '平衡': ['physical'],
        '走路': ['physical'],
        '站立': ['physical'],
        '手功能': ['physical'],
        '精细动作': ['physical'],

        // 日常生活相关
        '日常': ['daily'],
        '生活': ['daily'],
        '独立': ['daily'],
        '自理': ['daily'],
        '家庭': ['daily'],
        '环境': ['daily'],

        // 成功案例相关
        '成功': ['success_stories'],
        '案例': ['success_stories'],
        '故事': ['success_stories'],
        '经验': ['success_stories'],
        '希望': ['success_stories'],
        '信心': ['success_stories']
    }
};

// 视频推荐引擎
class VideoRecommendationEngine {
    static recommendVideos(userMessage, maxResults = 3) {
        const message = userMessage.toLowerCase();
        const recommendations = [];

        // 获取所有视频分类
        const categories = Object.keys(AI_KNOWLEDGE_BASE.videoDatabase);

        // 为每个分类计算匹配分数
        const categoryScores = {};

        for (const [keyword, categoryList] of Object.entries(AI_KNOWLEDGE_BASE.videoKeywordMapping)) {
            if (message.includes(keyword)) {
                categoryList.forEach(category => {
                    categoryScores[category] = (categoryScores[category] || 0) + 1;
                });
            }
        }

        // 按分数排序分类
        const sortedCategories = Object.entries(categoryScores)
            .sort(([,a], [,b]) => b - a)
            .map(([category]) => category);

        // 从匹配的分类中选择视频
        for (const category of sortedCategories) {
            if (recommendations.length >= maxResults) break;

            const videos = AI_KNOWLEDGE_BASE.videoDatabase[category] || [];
            const remainingSlots = maxResults - recommendations.length;

            // 选择该分类中的前几个视频
            const selectedVideos = videos.slice(0, remainingSlots);
            recommendations.push(...selectedVideos);
        }

        // 如果没有匹配的关键词，返回热门推荐
        if (recommendations.length === 0) {
            const allVideos = [];
            categories.forEach(category => {
                allVideos.push(...AI_KNOWLEDGE_BASE.videoDatabase[category]);
            });

            // 按观看次数排序，选择热门视频
            const popularVideos = allVideos
                .sort((a, b) => parseFloat(b.views) - parseFloat(a.views))
                .slice(0, maxResults);

            recommendations.push(...popularVideos);
        }

        return recommendations;
    }

    static formatVideoRecommendation(video) {
        return AI_KNOWLEDGE_BASE.responseTemplates.videoFormat
            .replace('{title}', video.title)
            .replace('{description}', video.description)
            .replace('{duration}', video.duration)
            .replace('{views}', video.views)
            .replace('{platform}', video.platform)
            .replace('{url}', video.url);
    }

    static generateVideoResponse(userMessage) {
        const videos = this.recommendVideos(userMessage);

        if (videos.length === 0) {
            return {
                text: '抱歉，暂时没有找到相关的康复视频。您可以访问我们的视频频道查看更多内容。',
                hasVideos: false,
                videos: []
            };
        }

        let responseText = '🎯 **为您推荐以下康复视频：**\n\n';

        videos.forEach((video, index) => {
            responseText += `**${index + 1}. ${video.title}**\n`;
            responseText += `📝 ${video.description}\n`;
            responseText += `⏱️ 时长：${video.duration} | 🔥 ${video.views}次观看\n`;
            responseText += `🔗 [观看${video.platform}视频](${video.url})\n\n`;
        });

        responseText += '💡 **温馨提示：**\n';
        responseText += '• 请在专业人士指导下进行康复训练\n';
        responseText += '• 训练强度要循序渐进，不要急于求成\n';
        responseText += '• 如有不适请及时停止并咨询医生\n\n';
        responseText += '希望这些视频对您的康复有所帮助！💪';

        return {
            text: responseText,
            hasVideos: true,
            videos: videos
        };
    }
}

// 智能回复生成器
class AIResponseGenerator {
    static generateResponse(userMessage) {
        const message = userMessage.toLowerCase();

        // 首先检查是否需要视频推荐
        const videoKeywords = Object.keys(AI_KNOWLEDGE_BASE.videoKeywordMapping);
        const needsVideoRecommendation = videoKeywords.some(keyword => message.includes(keyword)) ||
                                       message.includes('视频') ||
                                       message.includes('学习') ||
                                       message.includes('教程') ||
                                       message.includes('怎么做') ||
                                       message.includes('如何');

        if (needsVideoRecommendation) {
            const videoResponse = VideoRecommendationEngine.generateVideoResponse(userMessage);
            if (videoResponse.hasVideos) {
                return {
                    text: videoResponse.text,
                    module: 'videos',
                    moduleName: '短视频频道',
                    hasVideos: true,
                    videos: videoResponse.videos
                };
            }
        }

        // 检查是否询问特定功能
        for (const [key, feature] of Object.entries(AI_KNOWLEDGE_BASE.platformFeatures)) {
            if (feature.keywords.some(keyword => message.includes(keyword))) {
                return {
                    text: `${feature.description}\n\n您想了解更多详情吗？我可以为您提供直接链接。`,
                    module: key,
                    moduleName: feature.name
                };
            }
        }
        
        // 检查常见问题
        for (const [question, answer] of Object.entries(AI_KNOWLEDGE_BASE.commonQuestions)) {
            if (message.includes(question.toLowerCase()) || 
                question.toLowerCase().includes(message)) {
                return {
                    text: answer,
                    module: null,
                    moduleName: null
                };
            }
        }
        
        // 检查康复知识
        for (const [topic, knowledge] of Object.entries(AI_KNOWLEDGE_BASE.rehabilitationKnowledge)) {
            if (message.includes(topic.toLowerCase())) {
                return {
                    text: knowledge,
                    module: 'training',
                    moduleName: '训练康复'
                };
            }
        }
        
        // 默认回复
        return {
            text: '您好！我是康养平台智能助手。我可以帮您：\n\n• 了解平台各项功能\n• 快速导航到相关模块\n• 解答康复相关问题\n• 提供使用指导\n\n请告诉我您想了解什么？',
            module: null,
            moduleName: null
        };
    }
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DIFY_CONFIG, AI_KNOWLEDGE_BASE, AIResponseGenerator };
} else {
    window.DIFY_CONFIG = DIFY_CONFIG;
    window.AI_KNOWLEDGE_BASE = AI_KNOWLEDGE_BASE;
    window.AIResponseGenerator = AIResponseGenerator;
}
