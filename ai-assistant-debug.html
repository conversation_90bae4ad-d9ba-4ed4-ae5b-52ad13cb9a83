<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手调试页面</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .debug-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .debug-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .debug-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 智能助手调试页面</h1>
        
        <div class="debug-section">
            <h3>📋 加载状态检查</h3>
            <div id="loadingStatus">
                <div>CSS文件加载: <span id="cssStatus" class="status loading">检查中...</span></div>
                <div>HTML组件加载: <span id="htmlStatus" class="status loading">检查中...</span></div>
                <div>配置文件加载: <span id="configStatus" class="status loading">检查中...</span></div>
                <div>JavaScript文件加载: <span id="jsStatus" class="status loading">检查中...</span></div>
                <div>智能助手按钮: <span id="buttonStatus" class="status loading">检查中...</span></div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🧪 手动测试</h3>
            <button class="test-button" onclick="testFileAccess()">测试文件访问</button>
            <button class="test-button" onclick="manualLoadAssistant()">手动加载助手</button>
            <button class="test-button" onclick="checkDOMElements()">检查DOM元素</button>
            <button class="test-button" onclick="testDifyConfig()">测试Dify配置</button>
        </div>
        
        <div class="debug-section">
            <h3>📊 调试日志</h3>
            <div id="debugLog" class="debug-log">
                [调试开始] 智能助手调试页面已加载<br>
            </div>
        </div>
    </div>

    <script>
        // 调试日志函数
        function addLog(message, type = 'info') {
            const log = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#fc8181' : type === 'success' ? '#68d391' : '#63b3ed';
            log.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        // 测试文件访问
        async function testFileAccess() {
            addLog('开始测试文件访问...', 'info');
            
            const files = [
                '/components/ai-assistant.css',
                '/components/ai-assistant.html',
                '/components/ai-config.js',
                '/components/ai-assistant.js',
                '/components/load-assistant.js'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        addLog(`✓ ${file} - 访问成功`, 'success');
                    } else {
                        addLog(`✗ ${file} - 访问失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    addLog(`✗ ${file} - 网络错误: ${error.message}`, 'error');
                }
            }
        }

        // 手动加载智能助手
        async function manualLoadAssistant() {
            addLog('开始手动加载智能助手...', 'info');
            
            try {
                // 1. 加载CSS
                addLog('加载CSS文件...', 'info');
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = '/components/ai-assistant.css';
                document.head.appendChild(cssLink);
                updateStatus('cssStatus', 'success', '已加载');
                
                // 2. 加载HTML
                addLog('加载HTML组件...', 'info');
                const htmlResponse = await fetch('/components/ai-assistant.html');
                const htmlText = await htmlResponse.text();
                const div = document.createElement('div');
                div.innerHTML = htmlText;
                document.body.appendChild(div.firstElementChild);
                updateStatus('htmlStatus', 'success', '已加载');
                
                // 3. 加载配置
                addLog('加载配置文件...', 'info');
                const configScript = document.createElement('script');
                configScript.src = '/components/ai-config.js';
                document.head.appendChild(configScript);
                updateStatus('configStatus', 'success', '已加载');
                
                // 4. 加载主脚本
                addLog('加载主脚本...', 'info');
                const mainScript = document.createElement('script');
                mainScript.src = '/components/ai-assistant.js';
                document.head.appendChild(mainScript);
                updateStatus('jsStatus', 'success', '已加载');
                
                // 5. 检查按钮
                setTimeout(() => {
                    const button = document.querySelector('#aiAssistantToggle');
                    if (button) {
                        updateStatus('buttonStatus', 'success', '已显示');
                        addLog('✓ 智能助手按钮已显示', 'success');
                    } else {
                        updateStatus('buttonStatus', 'error', '未显示');
                        addLog('✗ 智能助手按钮未显示', 'error');
                    }
                }, 2000);
                
            } catch (error) {
                addLog(`✗ 加载失败: ${error.message}`, 'error');
            }
        }

        // 检查DOM元素
        function checkDOMElements() {
            addLog('检查DOM元素...', 'info');
            
            const elements = [
                '#aiAssistant',
                '#aiAssistantToggle',
                '#aiChatWindow',
                '.ai-toggle-btn'
            ];
            
            elements.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    addLog(`✓ 找到元素: ${selector}`, 'success');
                    addLog(`  - 样式: ${getComputedStyle(element).display}`, 'info');
                } else {
                    addLog(`✗ 未找到元素: ${selector}`, 'error');
                }
            });
        }

        // 测试Dify配置
        function testDifyConfig() {
            addLog('检查Dify配置...', 'info');
            
            if (window.DIFY_CONFIG) {
                addLog('✓ DIFY_CONFIG 已加载', 'success');
                addLog(`  - API Key: ${window.DIFY_CONFIG.apiKey}`, 'info');
                addLog(`  - App ID: ${window.DIFY_CONFIG.appId}`, 'info');
                addLog(`  - Base URL: ${window.DIFY_CONFIG.baseUrl}`, 'info');
            } else {
                addLog('✗ DIFY_CONFIG 未加载', 'error');
            }
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始自动检查...', 'info');
            
            // 延迟检查，给其他脚本加载时间
            setTimeout(() => {
                checkDOMElements();
                testDifyConfig();
            }, 1000);
        });
    </script>

    <!-- 尝试加载智能助手 -->
    <script src="/components/load-assistant.js"></script>
</body>
</html>
