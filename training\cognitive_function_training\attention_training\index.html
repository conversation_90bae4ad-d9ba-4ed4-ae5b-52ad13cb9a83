<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注意力训练 - 听词点击</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 训练介绍页面 -->
        <div id="intro-page" class="page active">
            <div class="intro-content">
                <h1 class="title">注意力训练 - 听词点击</h1>
                <div class="intro-text">
                    <h2>训练说明</h2>
                    <ul>
                        <li>系统会播放一段话，并显示目标词汇</li>
                        <li>当您听到目标词汇时，请立即点击"听到了"按钮</li>
                        <li>点击正确会得分，点击错误不得分</li>
                        <li>共有3道题目，每题播放完毕后会显示得分</li>
                        <li>请保持专注，仔细聆听</li>
                    </ul>
                </div>
                <button class="start-btn" onclick="startTraining()">开始训练</button>
            </div>
        </div>

        <!-- 训练页面 -->
        <div id="training-page" class="page">
            <div class="training-header">
                <div class="progress-info">
                    <span class="current-question">题目 <span id="current-num">1</span> / 3</span>
                    <span class="total-score">总分: <span id="total-score">0</span></span>
                </div>
            </div>

            <div class="training-content">
                <div class="target-word-display">
                    <h2>目标词汇</h2>
                    <div class="target-word" id="target-word">加载中...</div>
                </div>

                <div class="audio-controls">
                    <button class="play-btn" id="play-btn" onclick="playAudio()">
                        <span class="play-icon">▶</span>
                        播放语音
                    </button>
                    <div class="audio-progress">
                        <div class="progress-bar" id="progress-bar"></div>
                    </div>
                </div>

                <div class="click-area">
                    <button class="click-btn" id="click-btn" onclick="handleClick()" disabled>
                        听到了！
                    </button>
                    <div class="click-feedback" id="click-feedback"></div>
                    <button class="next-btn" id="next-btn" onclick="nextQuestion()" style="display: none;">下一题</button>
                </div>

                <div class="question-info">
                    <div class="question-score">本题得分: <span id="question-score">0</span></div>
                    <div class="expected-count">目标词出现次数: <span id="expected-count">0</span></div>
                    <div class="actual-count">您点击次数: <span id="actual-count">0</span></div>
                </div>
            </div>

            <div class="training-footer">
                <!-- 下一题按钮已移动到click-area中 -->
            </div>
        </div>

        <!-- 结果页面 -->
        <div id="result-page" class="page">
            <div class="result-content">
                <h1 class="result-title">训练完成！</h1>
                <div class="final-score">
                    <div class="score-display">
                        <span class="score-label">最终得分</span>
                        <span class="score-value" id="final-score">0</span>
                        <span class="score-total">/ 300</span>
                    </div>
                </div>
                <div class="result-details" id="result-details">
                    <!-- 详细结果将在这里显示 -->
                </div>
                <div class="result-actions">
                    <button class="restart-btn" onclick="restartTraining()">重新训练</button>
                    <button class="back-btn" onclick="goBack()">返回上级</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 音频元素 -->
    <audio id="audio-player" preload="none"></audio>

    <script src="script.js"></script>
</body>
</html>
