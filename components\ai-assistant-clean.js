// AI智能助手核心功能 - 清理版本
// 集成Dify API，提供智能对话和导航功能

// 避免重复声明
window.AIAssistant = window.AIAssistant || class AIAssistant {
    constructor() {
        this.isOpen = false;
        this.isMinimized = false;
        this.conversationId = null;

        // 从配置文件加载Dify API配置
        this.difyConfig = window.DIFY_CONFIG || {
            apiKey: '',
            baseUrl: '',
            appId: ''
        };

        // 平台功能映射
        this.moduleMap = {
            'training': '/training/index.html',
            'assessment': '/assessment/index.html',
            'forum': '/forum/index.html',
            'videos': '/videos/index.html',
            'points': '/points/index.html'
        };

        // 延迟初始化，确保DOM元素已加载
        setTimeout(() => this.init(), 100);
    }

    init() {
        console.log('初始化AI助手...');
        
        // 检查必要的DOM元素是否存在
        const toggleBtn = document.getElementById('aiAssistantToggle');
        const chatWindow = document.getElementById('aiChatWindow');
        
        if (!toggleBtn || !chatWindow) {
            console.error('AI助手DOM元素未找到:', {
                toggleBtn: !!toggleBtn,
                chatWindow: !!chatWindow
            });
            return;
        }
        
        console.log('DOM元素检查通过，开始绑定事件...');
        this.bindEvents();
        this.setupAutoResize();
        this.showWelcomeMessage();
        console.log('AI助手初始化完成');
    }
    
    bindEvents() {
        try {
            // 悬浮按钮点击事件
            const toggleBtn = document.getElementById('aiAssistantToggle');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', () => {
                    this.toggleChat();
                });
                console.log('悬浮按钮事件绑定成功');
            }
            
            // 最小化按钮
            const minimizeBtn = document.getElementById('aiMinimizeBtn');
            if (minimizeBtn) {
                minimizeBtn.addEventListener('click', () => {
                    this.minimizeChat();
                });
            }
            
            // 关闭按钮
            const closeBtn = document.getElementById('aiCloseBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.closeChat();
                });
            }
            
            // 发送按钮
            const sendBtn = document.getElementById('aiSendBtn');
            if (sendBtn) {
                sendBtn.addEventListener('click', () => {
                    this.sendMessage();
                });
            }
            
            // 输入框回车事件
            const messageInput = document.getElementById('aiMessageInput');
            if (messageInput) {
                messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }
            
            // 快捷按钮事件
            const quickBtns = document.querySelectorAll('.ai-quick-btn');
            quickBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.getAttribute('data-action');
                    this.handleQuickAction(action);
                });
            });
            
        } catch (error) {
            console.error('事件绑定失败:', error);
        }
    }
    
    toggleChat() {
        const chatWindow = document.getElementById('aiChatWindow');
        if (chatWindow) {
            this.isOpen = !this.isOpen;
            chatWindow.classList.toggle('active', this.isOpen);
            
            if (this.isOpen) {
                this.focusInput();
            }
        }
    }
    
    minimizeChat() {
        const chatWindow = document.getElementById('aiChatWindow');
        if (chatWindow) {
            this.isMinimized = !this.isMinimized;
            chatWindow.classList.toggle('minimized', this.isMinimized);
        }
    }
    
    closeChat() {
        const chatWindow = document.getElementById('aiChatWindow');
        if (chatWindow) {
            this.isOpen = false;
            chatWindow.classList.remove('active');
        }
    }
    
    focusInput() {
        const messageInput = document.getElementById('aiMessageInput');
        if (messageInput) {
            setTimeout(() => messageInput.focus(), 100);
        }
    }
    
    setupAutoResize() {
        const messageInput = document.getElementById('aiMessageInput');
        if (messageInput) {
            messageInput.addEventListener('input', () => {
                messageInput.style.height = 'auto';
                messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
            });
        }
    }
    
    showWelcomeMessage() {
        // 欢迎消息已在HTML中静态显示
        this.updateMessageTime();
    }
    
    updateMessageTime() {
        const timeElements = document.querySelectorAll('.ai-message-time');
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        timeElements.forEach(element => {
            if (!element.textContent) {
                element.textContent = timeString;
            }
        });
    }
    
    async sendMessage() {
        const messageInput = document.getElementById('aiMessageInput');
        const message = messageInput.value.trim();
        
        if (!message) return;
        
        // 清空输入框
        messageInput.value = '';
        messageInput.style.height = 'auto';
        
        // 显示用户消息
        this.addMessage(message, 'user');
        
        // 显示加载状态
        this.showLoading(true);
        
        try {
            // 调用Dify API
            const response = await this.callDifyAPI(message);
            this.addMessage(response, 'bot');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('抱歉，我现在无法回复您的消息。请稍后再试。', 'bot');
        } finally {
            this.showLoading(false);
        }
    }
    
    addMessage(content, type) {
        const messagesContainer = document.getElementById('aiChatMessages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ai-message-${type}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        if (type === 'user') {
            messageDiv.innerHTML = `
                <div class="ai-message-content ai-message-user">
                    <div class="ai-message-text">${this.escapeHtml(content)}</div>
                    <div class="ai-message-time">${timeString}</div>
                </div>
                <div class="ai-message-avatar ai-user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
        } else {
            // AI消息使用打字机效果
            messageDiv.innerHTML = `
                <div class="ai-message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-message-content">
                    <div class="ai-message-text"></div>
                    <div class="ai-message-time">${timeString}</div>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);

            // 开始打字机效果
            const textElement = messageDiv.querySelector('.ai-message-text');
            this.typewriterEffect(textElement, content);
        }

        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    typewriterEffect(element, content) {
        // 改进的打字机效果，支持边打边换行
        let index = 0;
        let displayText = '';

        const typeNextChar = () => {
            if (index < content.length) {
                displayText += content[index];

                // 实时处理当前文本，保持换行和格式
                const currentProcessed = this.processMessage(displayText);
                element.innerHTML = currentProcessed;

                // 滚动到底部
                const messagesContainer = document.getElementById('aiChatMessages');
                if (messagesContainer) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }

                index++;

                // 如果是换行符，稍微停顿一下
                const currentChar = content[index - 1];
                const delay = currentChar === '\n' ? 100 : 25;

                setTimeout(typeNextChar, delay);
            } else {
                // 完成后确保格式正确
                element.innerHTML = this.processMessage(content);
            }
        };

        typeNextChar();
    }
    
    processMessage(content) {
        // 处理换行符
        let processed = content.replace(/\n/g, '<br>');

        // 处理链接格式 [文本](链接)
        processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="ai-link">$1</a>');

        return processed;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showLoading(show) {
        const loadingIndicator = document.getElementById('aiLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = show ? 'flex' : 'none';
        }
    }
    
    async callDifyAPI(message) {
        // 首先尝试真实API调用
        if (this.difyConfig.apiKey && this.difyConfig.baseUrl) {
            try {
                const response = await fetch(`${this.difyConfig.baseUrl}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.difyConfig.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: message,
                        user: this.difyConfig.user || 'kangyang-user',
                        conversation_id: this.conversationId,
                        response_mode: 'blocking'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.conversation_id) {
                        this.conversationId = data.conversation_id;
                    }
                    return data.answer || '抱歉，我没有理解您的问题。';
                }
            } catch (error) {
                console.warn('Dify API调用失败，使用本地智能回复:', error.message);
            }
        }

        // 如果API调用失败，使用本地智能回复
        return this.getLocalResponse(message);
    }

    getLocalResponse(message) {
        const msg = message.toLowerCase();

        // 使用新的智能回复生成器
        if (window.AIResponseGenerator) {
            const response = window.AIResponseGenerator.generateResponse(message);
            if (response.hasVideos) {
                // 如果包含视频推荐，格式化显示
                return this.formatVideoResponse(response);
            }
            return response.text;
        }

        // 问候语
        if (msg.includes('你好') || msg.includes('您好') || msg.includes('hello')) {
            return `您好！我是康养平台智能助手 🤖

我可以帮您：
• 快速找到需要的康复功能
• 了解各种训练方法
• 制定个性化康复计划
• 解答康复相关问题
• 🎬 推荐相关康复视频

请告诉我您的康复需求？`;
        }

        // 记忆相关问题
        if (msg.includes('记忆') || msg.includes('忘记') || msg.includes('健忘')) {
            return `推荐训练：
• [记忆力训练](/training/cognitive_function_training/memory_training/index.html)
• [注意力训练](/training/cognitive_function_training/attention_training/index.html)
• [执行功能训练](/training/cognitive_function_training/executive_function_training/index.html)

使用建议：
• 每天训练30-60分钟
• 从简单难度开始
• 坚持4-6周见效果

记忆力训练需要持续坚持，相信您一定能看到改善！`;
        }

        // 语言相关问题
        if (msg.includes('说话') || msg.includes('语言') || msg.includes('表达') || msg.includes('沟通')) {
            return `推荐训练：
• [语言功能训练](/training/language_function_training/index.html)

使用建议：
• 每日练习发音和词汇
• 循序渐进，从简单开始
• 多与他人交流练习

语言康复需要耐心，每一次练习都是进步！`;
        }

        // 情绪相关问题
        if (msg.includes('心情') || msg.includes('抑郁') || msg.includes('焦虑') || msg.includes('情绪')) {
            return `推荐训练：
• [情绪调节训练](/training/emotional_regulation_training/index.html)

使用建议：
• 每天进行深呼吸练习
• 保持积极的生活态度
• 寻求家人和朋友的支持

情绪康复同样重要，您的努力会带来积极改变！`;
        }

        // 视觉相关问题
        if (msg.includes('视力') || msg.includes('看') || msg.includes('视觉') || msg.includes('眼睛')) {
            return `推荐训练：
• [视觉障碍训练](/training/visual_impairment_training/index.html)

使用建议：
• 定期进行视觉练习
• 注意用眼卫生
• 配合专业医生指导

视觉康复需要专业指导，请坚持训练！`;
        }

        // 评估相关
        if (msg.includes('评估') || msg.includes('测试') || msg.includes('检查') || msg.includes('进度')) {
            return `评估内容：
• [量表评估](/assessment/index.html)

使用建议：
• 定期进行评估（建议每月一次）
• 根据评估结果调整训练计划
• 记录康复进展

科学评估是康复成功的重要保障！`;
        }

        // 交流相关
        if (msg.includes('交流') || msg.includes('论坛') || msg.includes('病友') || msg.includes('分享')) {
            return `论坛功能：
• [老人论坛](/forum/index.html)

使用建议：
• 积极参与讨论
• 分享您的康复经验
• 互相鼓励支持

康复路上不孤单，大家一起加油！`;
        }

        // 学习相关
        if (msg.includes('学习') || msg.includes('视频') || msg.includes('教学') || msg.includes('知识')) {
            return `视频内容：
• [短视频频道](/videos/index.html)

使用建议：
• 定期观看学习
• 跟随视频练习
• 记录学习心得

知识是康复的力量，持续学习很重要！`;
        }

        // 奖励相关
        if (msg.includes('积分') || msg.includes('奖励') || msg.includes('礼品') || msg.includes('兑换')) {
            return `积分获取：
• [积分系统](/points/index.html)

使用建议：
• 坚持每日训练
• 积极参与社区活动
• 定期查看积分余额

积分激励让康复更有动力！`;
        }

        // 默认回复
        return `我是康养平台智能助手 🤖

推荐功能：
• [训练康复](/training/index.html)
• [量表评估](/assessment/index.html)
• [老人论坛](/forum/index.html)
• [短视频频道](/videos/index.html)

建议先进行量表评估，了解当前状况，然后选择合适的训练模块。

康复是一个循序渐进的过程，请保持耐心和信心！`;
    }

    formatVideoResponse(response) {
        // 创建包含视频推荐的富文本响应
        let formattedResponse = response.text;

        // 如果有视频推荐，添加交互式视频卡片
        if (response.videos && response.videos.length > 0) {
            formattedResponse += '\n\n📱 **快速访问：**\n';
            response.videos.forEach((video, index) => {
                formattedResponse += `• [${video.title}](${video.url})\n`;
            });
        }

        return formattedResponse;
    }

    handleQuickAction(action) {
        const actionMap = {
            'training': '我想了解训练康复功能',
            'assessment': '我想进行量表评估',
            'forum': '我想去老人论坛交流',
            'videos': '我想观看康复视频'
        };
        
        const message = actionMap[action];
        if (message) {
            const messageInput = document.getElementById('aiMessageInput');
            if (messageInput) {
                messageInput.value = message;
                this.sendMessage();
            }
        }
    }
};

// 避免重复初始化
if (!window.aiAssistantInstance) {
    console.log('准备初始化AI助手实例...');
    
    // 确保DOM和配置都已加载
    function initializeAssistant() {
        if (document.getElementById('aiAssistantToggle') && window.DIFY_CONFIG) {
            console.log('开始创建AI助手实例...');
            window.aiAssistantInstance = new window.AIAssistant();
            console.log('AI助手实例创建完成');
        } else {
            console.log('等待DOM元素和配置加载...');
            setTimeout(initializeAssistant, 200);
        }
    }
    
    // 立即尝试初始化，如果失败则等待
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAssistant);
    } else {
        initializeAssistant();
    }
}
