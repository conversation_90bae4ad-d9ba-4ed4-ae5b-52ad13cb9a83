<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脑卒中康养平台</title>
    <link rel="stylesheet" href="styles.css?v=2">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</head>
<body>
    <div class="container">
        <!-- 主标题 -->
        <header class="main-header">
            <h1 class="main-title">康养平台</h1>
            <p class="main-subtitle">专业的脑卒中康复服务平台</p>
        </header>

        <!-- 主要功能模块 -->
        <main class="main-content">
            <!-- 第一行：训练康复 和 量表评估 -->
            <div class="module-row">
                <div class="module-block" data-module="training" onclick="navigateToModule('training')">
                    <div class="module-icon">📊</div>
                    <div class="module-title">训练康复</div>
                    <div class="module-description">个性化康复训练方案，专业指导恢复</div>
                </div>
                <div class="module-block" data-module="assessment" onclick="navigateToModule('assessment')">
                    <div class="module-icon">📈</div>
                    <div class="module-title">量表评估</div>
                    <div class="module-description">科学评估康复进度，制定精准方案</div>
                </div>
            </div>

            <!-- 第二行：老人论坛 和 短视频频道 -->
            <div class="module-row">
                <div class="module-block" data-module="forum" onclick="navigateToModule('forum')">
                    <div class="module-icon">💬</div>
                    <div class="module-title">老人论坛</div>
                    <div class="module-description">交流康复经验，分享生活点滴</div>
                </div>
                <div class="module-block" data-module="videos" onclick="navigateToModule('videos')">
                    <div class="module-icon">📹</div>
                    <div class="module-title">短视频频道</div>
                    <div class="module-description">康复教学视频，轻松学习康复知识</div>
                </div>
            </div>

            <!-- 第三行：积分系统 -->
            <div class="module-row">
                <div class="module-block full-width" data-module="points" onclick="navigateToModule('points')">
                    <div class="module-icon">🎯</div>
                    <div class="module-title">积分系统</div>
                    <div class="module-description">完成任务获得积分，兑换精美礼品</div>
                </div>
            </div>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>&copy; 2024 脑卒中康养平台 - 关爱健康，陪伴康复</p>
        </footer>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-heartbeat"></i>
            <p>加载中...</p>
        </div>
    </div>

    <script src="script.js"></script>

    <!-- AI智能助手样式 -->
    <link rel="stylesheet" href="../components/ai-assistant.css">

    <!-- AI智能助手 - 直接嵌入版本 -->
    <div id="aiAssistant" class="ai-assistant">
        <!-- 悬浮按钮 -->
        <div id="aiAssistantToggle" class="ai-toggle-btn">
            <i class="fas fa-robot"></i>
            <span class="ai-pulse"></span>
        </div>

        <!-- 聊天窗口 -->
        <div id="aiChatWindow" class="ai-chat-window">
            <!-- 聊天头部 -->
            <div class="ai-chat-header">
                <div class="ai-header-info">
                    <i class="fas fa-robot"></i>
                    <span>康养助手</span>
                </div>
                <div class="ai-header-actions">
                    <button id="aiMinimizeBtn" class="ai-btn-minimize">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button id="aiCloseBtn" class="ai-btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- 聊天内容区域 -->
            <div class="ai-chat-content">
                <div id="aiChatMessages" class="ai-chat-messages">
                    <!-- 欢迎消息 -->
                    <div class="ai-message ai-message-bot">
                        <div class="ai-message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-message-content">
                            <div class="ai-message-text">
                                您好！我是康养平台智能助手，可以帮您：<br>
                                • 了解平台功能和使用方法<br>
                                • 快速导航到相关模块<br>
                                • 解答康复相关问题<br><br>
                                请问有什么可以帮助您的吗？
                            </div>
                            <div class="ai-message-time"></div>
                        </div>
                    </div>
                </div>

                <!-- 快捷功能按钮 -->
                <div class="ai-quick-actions">
                    <button class="ai-quick-btn" data-action="training">训练康复</button>
                    <button class="ai-quick-btn" data-action="assessment">量表评估</button>
                    <button class="ai-quick-btn" data-action="forum">老人论坛</button>
                    <button class="ai-quick-btn" data-action="videos">短视频</button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="ai-chat-input">
                <div class="ai-input-container">
                    <textarea
                        id="aiMessageInput"
                        class="ai-input-field"
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <button id="aiSendBtn" class="ai-send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="ai-input-hint">
                    按 Enter 发送，Shift + Enter 换行
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="aiLoadingIndicator" class="ai-loading-indicator">
                <div class="ai-loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>AI正在思考中...</span>
            </div>
        </div>
    </div>

    <!-- 加载配置和脚本 -->
    <script src="../components/ai-config.js"></script>
    <script src="../components/ai-assistant-clean.js"></script>
</body>
</html>
