<!-- AI智能助手悬浮窗组件 -->
<div id="aiAssistant" class="ai-assistant">
    <!-- 悬浮按钮 -->
    <div id="aiAssistantToggle" class="ai-toggle-btn">
        <i class="fas fa-robot"></i>
        <span class="ai-pulse"></span>
    </div>
    
    <!-- 聊天窗口 -->
    <div id="aiChatWindow" class="ai-chat-window">
        <!-- 聊天头部 -->
        <div class="ai-chat-header">
            <div class="ai-header-info">
                <i class="fas fa-robot"></i>
                <span>康养助手</span>
            </div>
            <div class="ai-header-actions">
                <button id="aiMinimizeBtn" class="ai-btn-minimize">
                    <i class="fas fa-minus"></i>
                </button>
                <button id="aiCloseBtn" class="ai-btn-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <!-- 聊天内容区域 -->
        <div class="ai-chat-content">
            <div id="aiChatMessages" class="ai-chat-messages">
                <!-- 欢迎消息 -->
                <div class="ai-message ai-message-bot">
                    <div class="ai-message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-message-content">
                        <div class="ai-message-text">
                            您好！我是康养平台智能助手，可以帮您：<br>
                            • 了解平台功能和使用方法<br>
                            • 快速导航到相关模块<br>
                            • 解答康复相关问题<br><br>
                            请问有什么可以帮助您的吗？
                        </div>
                        <div class="ai-message-time"></div>
                    </div>
                </div>
            </div>
            
            <!-- 快捷功能按钮 -->
            <div class="ai-quick-actions">
                <button class="ai-quick-btn" data-action="training">训练康复</button>
                <button class="ai-quick-btn" data-action="assessment">量表评估</button>
                <button class="ai-quick-btn" data-action="forum">老人论坛</button>
                <button class="ai-quick-btn" data-action="videos">短视频</button>
            </div>
        </div>
        
        <!-- 输入区域 -->
        <div class="ai-chat-input">
            <div class="ai-input-container">
                <textarea 
                    id="aiMessageInput" 
                    class="ai-input-field" 
                    placeholder="请输入您的问题..."
                    rows="1"
                ></textarea>
                <button id="aiSendBtn" class="ai-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="ai-input-hint">
                按 Enter 发送，Shift + Enter 换行
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="aiLoadingIndicator" class="ai-loading-indicator">
            <div class="ai-loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span>AI正在思考中...</span>
        </div>
    </div>
</div>
