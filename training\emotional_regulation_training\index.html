<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情绪调节训练 - 康养平台</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 16px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .training-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }
        .training-item h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }
        .training-item p {
            color: #666;
            margin-bottom: 15px;
        }
        .zoom-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .zoom-btn:hover {
            background: #5a67d8;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 放大缩小控制 -->
    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">
            <i class="fas fa-plus"></i>
        </button>
        <button class="zoom-btn" onclick="zoomOut()">
            <i class="fas fa-minus"></i>
        </button>
        <button class="zoom-btn" onclick="resetZoom()">
            <i class="fas fa-undo"></i>
        </button>
    </div>

    <div class="container">
        <a href="/training/index.html" class="back-link">← 返回训练康复</a>
        
        <div class="header">
            <h1>😊 情绪调节训练</h1>
            <p>调节情绪状态，提升心理健康</p>
        </div>
        
        <div class="training-item">
            <h3>🎭 情绪识别训练</h3>
            <p>学习识别和理解不同的情绪状态</p>
            <p>• 面部表情识别</p>
            <p>• 情绪词汇学习</p>
            <p>• 情绪强度判断</p>
        </div>
        
        <div class="training-item">
            <h3>🧘 放松训练</h3>
            <p>掌握有效的放松技巧</p>
            <p>• 深呼吸练习</p>
            <p>• 渐进性肌肉放松</p>
            <p>• 冥想练习</p>
        </div>
        
        <div class="training-item">
            <h3>💭 认知重构</h3>
            <p>改变负性思维模式</p>
            <p>• 负性思维识别</p>
            <p>• 积极思维培养</p>
            <p>• 认知偏差纠正</p>
        </div>
        
        <div class="training-item">
            <h3>🎯 情绪管理策略</h3>
            <p>学习实用的情绪管理方法</p>
            <p>• 情绪调节技巧</p>
            <p>• 压力应对策略</p>
            <p>• 社交技能训练</p>
        </div>
    </div>

    <script>
        let currentZoom = 100;
        
        function zoomIn() {
            currentZoom += 10;
            if (currentZoom > 150) currentZoom = 150;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function zoomOut() {
            currentZoom -= 10;
            if (currentZoom < 80) currentZoom = 80;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function resetZoom() {
            currentZoom = 100;
            document.body.style.zoom = '100%';
        }
    </script>

    <!-- AI智能助手 -->
    <script src="../../components/load-assistant.js"></script>
</body>
</html>
