/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 确保html和body也是全屏 */
html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

/* 基础样式 */
body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
    color: #2c3e50;
    line-height: 1.6;
    width: 100vw; /* 确保全屏宽度 */
    height: 100vh; /* 确保全屏高度 */
    font-size: 28px; /* 显著增大基础字体 */
    overflow-x: hidden; /* 防止横向滚动 */
    margin: 0;
    padding: 0;
}

.container {
    width: 100vw; /* 全屏宽度 */
    height: 100vh; /* 全屏高度 */
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed; /* 固定定位确保全屏 */
    top: 0;
    left: 0;
}

/* 页面切换 */
.page {
    display: none;
    width: 100vw;
    height: 100vh;
    padding: 20px; /* 减小页面内边距 */
    overflow-y: auto;
    box-sizing: border-box; /* 确保padding包含在总尺寸内 */
}

.page.active {
    display: block;
}

/* 介绍页面样式 */
.intro-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 80px 60px; /* 增大内边距 */
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: calc(100vw - 80px); /* 全屏宽度减去边距 */
    height: calc(100vh - 80px); /* 全屏高度减去边距 */
    max-width: none; /* 移除最大宽度限制 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.title {
    font-size: 4.5rem; /* 显著增大标题字体 */
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 40px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.intro-text {
    text-align: left;
    margin-bottom: 40px;
}

.intro-text h2 {
    font-size: 2.4rem; /* 增大副标题字体 */
    color: #34495e;
    margin-bottom: 20px;
    text-align: center;
}

.intro-text ul {
    list-style: none;
    padding: 0;
}

.intro-text li {
    font-size: 1.6rem; /* 增大列表项字体 */
    margin-bottom: 20px; /* 增大间距 */
    padding-left: 40px; /* 增大左边距 */
    position: relative;
    color: #555;
}

.intro-text li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.4rem;
}

.start-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 30px 80px; /* 显著增大按钮 */
    font-size: 2.4rem; /* 显著增大按钮字体 */
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.start-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

/* 训练页面样式 */
.training-header {
    background: rgba(255, 255, 255, 0.9);
    padding: 30px 40px; /* 增大内边距 */
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 2.0rem; /* 显著增大字体 */
    font-weight: 500;
}

.current-question {
    color: #3498db;
}

.total-score {
    color: #27ae60;
}

.training-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 30px; /* 进一步减小内边距 */
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-align: center;
    width: calc(100% - 40px); /* 相对于页面宽度 */
    max-width: 1000px;
    margin: 0 auto;
    height: calc(100vh - 80px); /* 调整高度计算 */
    display: flex;
    flex-direction: column;
    justify-content: space-evenly; /* 更均匀的分布 */
    box-sizing: border-box;
}

.target-word-display {
    margin: 0; /* 移除边距，让flex布局控制间距 */
}

.target-word-display h2 {
    font-size: 2.5rem; /* 稍微减小字体 */
    color: #34495e;
    margin: 0 0 8px 0; /* 减小边距 */
}

.target-word {
    font-size: 4rem; /* 进一步减小字体 */
    font-weight: 700;
    color: #e74c3c;
    background: linear-gradient(135deg, #fff5f5, #ffe6e6);
    padding: 15px 40px; /* 进一步减小内边距 */
    border-radius: 15px;
    border: 3px solid #e74c3c; /* 减小边框 */
    display: inline-block;
    min-width: 250px;
}

.audio-controls {
    margin: 0; /* 移除边距，让flex布局控制间距 */
}

.play-btn {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border: none;
    padding: 12px 40px; /* 进一步减小内边距 */
    font-size: 1.6rem; /* 减小字体 */
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto; /* 移除下边距 */
}

.play-btn:hover {
    background: linear-gradient(135deg, #229954, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.play-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.play-icon {
    font-size: 1.2rem;
}

.audio-progress {
    width: 100%;
    height: 12px; /* 增大进度条高度 */
    background: #ecf0f1;
    border-radius: 6px; /* 增大圆角 */
    overflow: hidden;
    margin: 0 auto;
    max-width: 600px; /* 增大最大宽度 */
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.1s ease;
    border-radius: 4px;
}

.click-area {
    margin: 0; /* 移除边距，让flex布局控制间距 */
}

.click-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 18px 60px; /* 进一步减小内边距 */
    font-size: 2.2rem; /* 减小字体 */
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
    margin: 0; /* 移除边距 */
}

.click-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.click-btn:active:not(:disabled) {
    transform: translateY(-1px) scale(0.95);
    animation: clickPulse 0.3s ease;
}

@keyframes clickPulse {
    0% { transform: translateY(-3px) scale(1); }
    50% { transform: translateY(-1px) scale(0.95); box-shadow: 0 4px 15px rgba(243, 156, 18, 0.6); }
    100% { transform: translateY(-3px) scale(1); }
}

.click-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.click-feedback {
    font-size: 1.8rem; /* 增大字体适应全屏 */
    font-weight: 600;
    min-height: 50px; /* 增加最小高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 25px; /* 增大内边距 */
    border-radius: 25px; /* 增大圆角 */
    margin-top: 60px; /* 进一步增加上边距，拉开与"听到了"按钮的距离 */
    transition: all 0.3s ease;
}

.click-feedback.correct {
    color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
    border: 2px solid rgba(39, 174, 96, 0.3);
}

.click-feedback.incorrect {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid rgba(231, 76, 60, 0.3);
}

.click-feedback.playing {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
    border: 2px solid rgba(52, 152, 219, 0.3);
    animation: pulse 1.5s infinite;
}

.click-feedback.completed {
    color: #7f8c8d;
    background: rgba(127, 140, 141, 0.1);
    border: 2px solid rgba(127, 140, 141, 0.3);
}

.click-feedback.limit-reached {
    color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
    border: 2px solid rgba(243, 156, 18, 0.3);
    animation: warning-pulse 1s infinite;
}

.click-feedback.checking {
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    border: 2px solid rgba(108, 117, 125, 0.3);
    opacity: 0.8;
    animation: checking-pulse 0.8s infinite;
}

@keyframes warning-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.8;
    }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes checking-pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

.question-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* 增大最小宽度 */
    gap: 30px; /* 增大间距 */
    margin-top: 40px; /* 增大上边距 */
    padding-top: 40px; /* 增大内边距 */
    border-top: 2px solid #ecf0f1;
}

.question-info > div {
    background: #f8f9fa;
    padding: 30px; /* 显著增大内边距 */
    border-radius: 10px;
    font-size: 1.8rem; /* 显著增大字体 */
    font-weight: 500;
}

.question-score {
    color: #27ae60;
}

.expected-count {
    color: #3498db;
}

.actual-count {
    color: #9b59b6;
}

.training-footer {
    text-align: center;
    margin-top: 30px;
}

.next-btn {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    border: none;
    padding: 20px 60px; /* 增大内边距适应全屏 */
    font-size: 2rem; /* 增大字体适应全屏 */
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3); /* 增强阴影 */
    margin-top: 20px; /* 添加上边距 */
}

.next-btn:hover {
    background: linear-gradient(135deg, #8e44ad, #7d3c98);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* 结果页面样式 */
.result-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.result-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: #27ae60;
    margin-bottom: 40px;
}

.final-score {
    margin-bottom: 40px;
}

.score-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 2rem;
    font-weight: 600;
}

.score-label {
    color: #34495e;
}

.score-value {
    color: #27ae60;
    font-size: 3rem;
}

.score-total {
    color: #7f8c8d;
}

.result-details {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 40px;
    text-align: left;
}

.result-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.restart-btn, .back-btn {
    padding: 15px 30px;
    font-size: 1.2rem;
    font-weight: 500;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.restart-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.restart-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.back-btn {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.back-btn:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2.2rem;
    }
    
    .target-word {
        font-size: 2.2rem;
        padding: 15px 25px;
        min-width: 150px;
    }
    
    .click-btn {
        padding: 20px 40px;
        font-size: 1.5rem;
    }
    
    .question-info {
        grid-template-columns: 1fr;
    }
    
    .result-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .restart-btn, .back-btn {
        width: 200px;
    }
}
