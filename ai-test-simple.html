<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手简单测试</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-container h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .test-container p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        .status-box {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            text-align: left;
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 智能助手简单测试</h1>
        <p>这个页面用于测试智能助手是否能正常加载和显示</p>
        
        <div id="statusBox" class="status-box">
            <div id="statusText">正在加载智能助手...</div>
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>页面加载后，智能助手会自动开始加载</li>
                <li>如果成功，您会在右下角看到一个蓝色的机器人按钮</li>
                <li>点击按钮可以打开聊天窗口</li>
                <li>如果失败，状态框会显示错误信息</li>
                <li>请按F12查看控制台获取详细信息</li>
            </ol>
        </div>
    </div>

    <script>
        // 状态更新函数
        function updateStatus(message, isSuccess = null) {
            const statusBox = document.getElementById('statusBox');
            const statusText = document.getElementById('statusText');
            
            statusText.textContent = message;
            
            if (isSuccess === true) {
                statusBox.className = 'status-box status-success';
            } else if (isSuccess === false) {
                statusBox.className = 'status-box status-error';
            } else {
                statusBox.className = 'status-box';
            }
        }

        // 检查智能助手加载状态
        function checkAssistantStatus() {
            const button = document.querySelector('#aiAssistantToggle');
            const assistant = document.querySelector('#aiAssistant');
            
            if (button && assistant) {
                updateStatus('✅ 智能助手加载成功！右下角应该有聊天按钮', true);
                console.log('智能助手加载成功');
                return true;
            } else {
                updateStatus('❌ 智能助手加载失败，请查看控制台错误信息', false);
                console.log('智能助手加载失败');
                return false;
            }
        }

        // 页面加载完成后开始检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始检查智能助手...');
            
            // 延迟检查，给智能助手加载时间
            setTimeout(() => {
                if (!checkAssistantStatus()) {
                    // 如果第一次检查失败，再等待一段时间重试
                    setTimeout(() => {
                        checkAssistantStatus();
                    }, 3000);
                }
            }, 2000);
        });

        // 监听智能助手加载事件
        window.addEventListener('load', function() {
            // 最终检查
            setTimeout(() => {
                checkAssistantStatus();
            }, 5000);
        });
    </script>

    <!-- 加载智能助手 -->
    <script src="/components/load-assistant.js"></script>
</body>
</html>
