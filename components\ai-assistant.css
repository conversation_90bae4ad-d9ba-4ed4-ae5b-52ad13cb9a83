/* AI智能助手悬浮窗样式 */
.ai-assistant {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: 'Noto Sans SC', sans-serif;
}

/* 悬浮按钮样式 */
.ai-toggle-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.ai-toggle-btn i {
    color: white;
    font-size: 24px;
}

.ai-toggle-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25);
}

/* 脉冲动画 */
.ai-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(118, 75, 162, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.2);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 聊天窗口样式 */
.ai-chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    pointer-events: none;
}

.ai-chat-window.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

/* 聊天头部样式 */
.ai-chat-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-header-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.ai-header-info i {
    font-size: 18px;
}

.ai-header-actions {
    display: flex;
    gap: 10px;
}

.ai-btn-minimize, .ai-btn-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.ai-btn-minimize:hover, .ai-btn-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 聊天内容区域样式 */
.ai-chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 消息样式 */
.ai-message {
    display: flex;
    gap: 10px;
    max-width: 85%;
}

.ai-message-bot {
    align-self: flex-start;
}

.ai-message-user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.ai-message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-message-bot .ai-message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-message-user .ai-message-avatar {
    background-color: #e6f7ff;
    color: #1890ff;
}

.ai-message-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.ai-message-text {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.5;
}

.ai-message-bot .ai-message-text {
    background-color: #f5f5f5;
    border-top-left-radius: 4px;
}

.ai-message-user .ai-message-text {
    background-color: #e6f7ff;
    color: #333;
    border-top-right-radius: 4px;
}

.ai-message-time {
    font-size: 11px;
    color: #999;
    align-self: flex-end;
}

.ai-encouragement {
    font-size: 0.85em !important;
    color: #888 !important;
    opacity: 0.8;
    margin-top: 8px;
    font-style: italic;
}

.ai-thinking {
    color: #999 !important;
    opacity: 0.7;
}

/* 快捷功能按钮 */
.ai-quick-actions {
    display: flex;
    gap: 8px;
    padding: 10px 20px;
    overflow-x: auto;
    scrollbar-width: none;
}

.ai-quick-actions::-webkit-scrollbar {
    display: none;
}

.ai-quick-btn {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 16px;
    font-size: 12px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-quick-btn:hover {
    background-color: #e6f7ff;
}

/* 输入区域样式 */
.ai-chat-input {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
}

.ai-input-container {
    display: flex;
    gap: 10px;
    background-color: #f5f5f5;
    border-radius: 24px;
    padding: 8px 16px;
}

.ai-input-field {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    max-height: 100px;
    min-height: 24px;
}

.ai-send-btn {
    background: none;
    border: none;
    color: #764ba2;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.ai-send-btn:hover {
    background-color: rgba(118, 75, 162, 0.1);
}

.ai-input-hint {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
    text-align: right;
}

/* 加载状态 */
.ai-loading-indicator {
    position: absolute;
    bottom: 80px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.ai-loading-indicator.active {
    opacity: 1;
    transform: translateY(0);
}

.ai-loading-dots {
    display: flex;
    gap: 4px;
}

.ai-loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: white;
    animation: loading 1.4s infinite ease-in-out both;
}

.ai-loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.ai-loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading {
    0%, 80%, 100% { 
        transform: scale(0);
    } 
    40% { 
        transform: scale(1.0);
    }
}

/* 导航链接样式 */
.ai-nav-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.ai-nav-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(118, 75, 162, 0.3);
}

.ai-nav-link i {
    font-size: 12px;
}

/* 滚动条样式 */
.ai-chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 消息动画 */
.ai-message {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 打字效果 */
.ai-typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-radius: 18px;
    border-top-left-radius: 4px;
    max-width: 80px;
}

.ai-typing-dots {
    display: flex;
    gap: 3px;
}

.ai-typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.ai-typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.ai-typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-assistant {
        bottom: 15px;
        right: 15px;
    }

    .ai-toggle-btn {
        width: 55px;
        height: 55px;
    }

    .ai-toggle-btn i {
        font-size: 22px;
    }

    .ai-chat-window {
        width: calc(100vw - 30px);
        height: 70vh;
        bottom: 75px;
        right: 0;
        left: 15px;
    }

    .ai-quick-actions {
        flex-wrap: wrap;
    }

    .ai-quick-btn {
        flex: 1;
        min-width: calc(50% - 4px);
    }
}

@media (max-width: 480px) {
    .ai-chat-window {
        height: 75vh;
        bottom: 70px;
    }

    .ai-chat-header {
        padding: 12px 15px;
    }

    .ai-chat-messages {
        padding: 15px;
    }

    .ai-chat-input {
        padding: 12px 15px;
    }

    .ai-quick-btn {
        font-size: 11px;
        padding: 6px 10px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .ai-chat-window {
        background-color: #2d2d2d;
        color: #ffffff;
    }

    .ai-message-bot .ai-message-text {
        background-color: #3d3d3d;
        color: #ffffff;
    }

    .ai-message-user .ai-message-text {
        background-color: #4a5568;
        color: #ffffff;
    }

    .ai-input-container {
        background-color: #3d3d3d;
    }

    .ai-input-field {
        color: #ffffff;
    }

    .ai-input-field::placeholder {
        color: #a0a0a0;
    }

    .ai-quick-btn {
        background-color: #3d3d3d;
        color: #ffffff;
    }

    .ai-quick-btn:hover {
        background-color: #4a5568;
    }
}
