<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻醉智能体 - API版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            min-height: calc(100vh - 40px);
            display: flex;
        }

        .sidebar {
            width: 320px;
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #495057;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .logo p {
            color: #6c757d;
            font-size: 14px;
        }

        .config-section {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .config-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
            font-size: 13px;
        }

        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 13px;
        }

        .user-type {
            margin-bottom: 25px;
        }

        .user-type h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .type-buttons {
            display: flex;
            gap: 10px;
        }

        .type-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            font-size: 13px;
        }

        .type-btn.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }

        .quick-questions {
            margin-bottom: 20px;
        }

        .quick-questions h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .question-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 6px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
            color: #495057;
        }

        .question-btn:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .chat-header h2 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 5px;
        }

        .chat-header p {
            color: #6c757d;
            font-size: 14px;
        }

        .chat-area {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            background: #ffffff;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #007bff;
            color: white;
        }

        .message.ai .message-avatar {
            background: #28a745;
            color: white;
        }

        .message-content {
            max-width: 75%;
            padding: 12px 16px;
            border-radius: 16px;
            line-height: 1.5;
            font-size: 14px;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.ai .message-content {
            background: #f8f9fa;
            color: #495057;
            border-bottom-left-radius: 4px;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 16px;
            border-bottom-left-radius: 4px;
            max-width: 75%;
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #6c757d;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .input-area {
            padding: 20px 30px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-box {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 22px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-box:focus {
            border-color: #007bff;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: #0056b3;
        }

        .send-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .voice-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: #28a745;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background 0.3s;
            margin-left: 8px;
        }

        .voice-btn:hover {
            background: #218838;
        }

        .voice-btn.recording {
            background: #dc3545;
            animation: pulse 1s infinite;
        }

        .voice-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .speak-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 12px;
            cursor: pointer;
            margin-left: 8px;
            transition: background 0.3s;
        }

        .speak-btn:hover {
            background: #138496;
        }

        .speak-btn.speaking {
            background: #ffc107;
            color: #000;
            animation: pulse 1s infinite;
        }

        .speak-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .status-bar {
            padding: 8px 30px;
            background: #e9ecef;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .status-connected { background: #28a745; }
        .status-connecting { background: #ffc107; }
        .status-error { background: #dc3545; }

        .progress-container {
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #e9ecef;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 11px;
            color: #6c757d;
            text-align: center;
        }

        .log-container {
            margin-top: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-container h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: #495057;
        }

        .log-entry {
            font-size: 11px;
            margin-bottom: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }

        .message-content pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
            font-family: inherit;
        }

        .message-content .thinking-content {
            font-size: 10px !important;
            color: #888888 !important;
            font-style: italic !important;
            font-weight: normal !important;
            margin-bottom: 8px;
            padding: 6px 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #dee2e6;
            line-height: 1.3;
        }

        .main-content-wrapper {
            font-size: 14px;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .sidebar {
                width: 100%;
                padding: 20px;
            }

            .type-buttons {
                flex-direction: column;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="logo">
                <h1>🏥 麻醉智能体</h1>
                <p>基于Dify API的专业咨询</p>
            </div>

            <div class="config-section">
                <h3>⚙️ API配置</h3>
                <div class="input-group">
                    <label>API地址:</label>
                    <input type="text" id="apiUrl" value="http://127.0.0.1/v1" placeholder="http://127.0.0.1/v1">
                </div>
                <div class="input-group">
                    <label>患者版API Key:</label>
                    <input type="password" id="patientApiKey" value="app-nBdS7KfSs39LyT5RbXWUINTf" placeholder="患者版API密钥">
                </div>
                <div class="input-group">
                    <label>医生版API Key:</label>
                    <input type="password" id="doctorApiKey" value="app-Ixk717ELfvik8JwoLDS1dU7O" placeholder="医生版API密钥">
                </div>
                <div class="input-group" style="margin-top: 10px;">
                    <button onclick="exportConfig()" style="padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px;">📤 导出配置</button>
                    <button onclick="importConfig()" style="padding: 5px 10px; background: #ffc107; color: #000; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px;">📥 导入配置</button>
                    <button onclick="testVoice()" style="padding: 5px 10px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">🔊 测试语音</button>
                    <input type="file" id="configFileInput" accept=".json" style="display: none;" onchange="handleConfigImport(event)">
                </div>
            </div>

            <div class="user-type">
                <h3>👤 用户类型</h3>
                <div class="type-buttons">
                    <button class="type-btn active" data-type="patient">
                        😊 患者版
                    </button>
                    <button class="type-btn" data-type="doctor">
                        👨‍⚕️ 医生版
                    </button>
                </div>
            </div>

            <div class="quick-questions">
                <h3>💡 快捷问题</h3>
                <div id="patientQuestions">
                    <button class="question-btn">麻醉安全吗？有什么风险？</button>
                    <button class="question-btn">麻醉前需要注意什么？</button>
                    <button class="question-btn">麻醉过程是怎样的？</button>
                    <button class="question-btn">麻醉后多久能醒来？</button>
                    <button class="question-btn">全身麻醉和局部麻醉哪个好？</button>
                </div>
                <div id="doctorQuestions" style="display: none;">
                    <button class="question-btn">困难气道的处理策略</button>
                    <button class="question-btn">老年患者麻醉风险评估</button>
                    <button class="question-btn">术中低血压的处理原则</button>
                    <button class="question-btn">肌松药的选择和监测</button>
                    <button class="question-btn">区域麻醉并发症预防</button>
                </div>
            </div>

            <div class="log-container">
                <h4>📊 调试日志</h4>
                <div id="logArea"></div>
                <button onclick="testThinkingContent()" style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
                    🧪 测试思考内容
                </button>
                <button onclick="testStreamingThinking()" style="margin-top: 5px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
                    🔄 测试流式思考
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-header">
                <h2 id="chatTitle">患者咨询 - 麻醉科普</h2>
                <p id="chatSubtitle">温暖专业的麻醉知识科普和心理安慰</p>
            </div>

            <div class="chat-area" id="chatArea">
                <!-- 消息将在这里显示 -->
            </div>

            <div class="input-area">
                <div class="input-container">
                    <textarea 
                        class="input-box" 
                        id="messageInput" 
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <button class="voice-btn" id="voiceBtn" title="语音输入">
                        🎤
                    </button>
                    <button class="send-btn" id="sendBtn">
                        ➤
                    </button>
                </div>
            </div>

            <div class="status-bar">
                <span class="status-indicator status-connecting" id="statusIndicator"></span>
                <span id="statusText">准备就绪</span>
            </div>
        </div>
    </div>

    <script>
        // 配置和状态
        let currentType = 'patient';
        let currentConversationId = '';
        let isStreaming = false;
        let currentStreamingMessage = null;
        let currentThinkingContent = '';
        let currentMainContent = '';
        let rawStreamContent = ''; // 累积原始流式内容

        // 语音功能相关
        let recognition = null;
        let isRecording = false;
        let speechSynthesis = window.speechSynthesis;
        let currentUtterance = null;

        // DOM 元素
        const typeButtons = document.querySelectorAll('.type-btn');
        const chatTitle = document.getElementById('chatTitle');
        const chatSubtitle = document.getElementById('chatSubtitle');
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const voiceBtn = document.getElementById('voiceBtn');
        const patientQuestions = document.getElementById('patientQuestions');
        const doctorQuestions = document.getElementById('doctorQuestions');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const logArea = document.getElementById('logArea');

        // 配置信息
        const CONFIG = {
            patient: {
                title: "患者咨询 - 麻醉科普",
                subtitle: "温暖专业的麻醉知识科普和心理安慰"
            },
            doctor: {
                title: "医生咨询 - 专业建议",
                subtitle: "基于循证医学的专业麻醉学咨询服务"
            }
        };

        // 初始化
        function init() {
            updateInterface();
            addWelcomeMessage();
            
            // 事件监听
            typeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    typeButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    currentType = btn.dataset.type;
                    updateInterface();
                    clearChat();
                    addWelcomeMessage();
                });
            });

            // 快捷问题点击
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('question-btn')) {
                    messageInput.value = e.target.textContent;
                    sendMessage();
                }
            });

            // 发送消息
            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 自动调整输入框高度
            messageInput.addEventListener('input', () => {
                messageInput.style.height = 'auto';
                messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
            });
        }

        // 更新界面
        function updateInterface() {
            const config = CONFIG[currentType];
            chatTitle.textContent = config.title;
            chatSubtitle.textContent = config.subtitle;

            // 切换问题列表
            if (currentType === 'patient') {
                patientQuestions.style.display = 'block';
                doctorQuestions.style.display = 'none';
            } else {
                patientQuestions.style.display = 'none';
                doctorQuestions.style.display = 'block';
            }
        }

        // 添加欢迎消息
        function addWelcomeMessage() {
            const welcomeMessages = {
                patient: "您好！我是您的麻醉科医生助手。我理解您对即将到来的手术可能会有一些担心和疑问，这是完全正常的。现代麻醉技术已经非常成熟和安全，我们有完善的监护设备和专业的医疗团队来确保您的安全。无论您有什么疑问或担忧，都可以随时向我咨询。😊",
                doctor: "欢迎使用麻醉学专业咨询系统！我是您的麻醉学专家顾问，基于最新的临床指南和循证医学证据，为您提供专业的麻醉学咨询服务。可为您解答麻醉方案选择与优化、复杂病例管理策略、并发症预防与处理等问题。🩺"
            };
            
            addMessage(welcomeMessages[currentType], 'ai');
        }

        // 清空聊天
        function clearChat() {
            chatArea.innerHTML = '';
            currentConversationId = '';
            addLog('清空聊天记录，重置对话ID', 'info');
        }

        // 添加消息
        function addMessage(content, type, isStreaming = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? '👤' : '🩺';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (isStreaming) {
                messageContent.innerHTML = `<pre>${content}</pre>`;
                messageDiv.id = 'streaming-message';
            } else {
                // 处理换行
                if (content.includes('\n')) {
                    messageContent.innerHTML = `<pre>${content}</pre>`;
                } else {
                    messageContent.textContent = content;
                }

                // 为AI消息添加语音播放按钮
                if (type === 'ai' && content.trim()) {
                    const speakButton = document.createElement('button');
                    speakButton.className = 'speak-btn';
                    speakButton.textContent = '🔊 播放';
                    speakButton.setAttribute('data-text', encodeURIComponent(content));
                    messageContent.appendChild(speakButton);
                }
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;

            addLog(`添加${type === 'user' ? '用户' : 'AI'}消息: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`, 'info');

            return messageContent;
        }

        // 显示打字指示器
        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message ai';
            typingDiv.id = 'typing-indicator';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🩺';

            const typingContent = document.createElement('div');
            typingContent.className = 'typing-indicator show';
            typingContent.innerHTML = `
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;

            typingDiv.appendChild(avatar);
            typingDiv.appendChild(typingContent);
            chatArea.appendChild(typingDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 日志功能
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;

            // 限制日志条数
            if (logArea.children.length > 50) {
                logArea.removeChild(logArea.firstChild);
            }
        }

        // 进度条功能
        function createProgressBar() {
            const progressContainer = document.createElement('div');
            progressContainer.className = 'progress-container';
            progressContainer.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备发送消息...</div>
            `;
            return progressContainer;
        }

        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            if (progressFill) progressFill.style.width = `${percentage}%`;
            if (progressText) progressText.textContent = text;
        }

        function removeProgress() {
            const progressContainer = document.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }
        }

        // 更新状态
        function updateStatus(status, message) {
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = message;
            addLog(`状态更新: ${message}`, status === 'error' ? 'error' : 'info');
        }

        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isStreaming) return;

            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = currentType === 'patient'
                ? document.getElementById('patientApiKey').value
                : document.getElementById('doctorApiKey').value;

            if (!apiKey) {
                alert('请先配置API密钥');
                addLog('错误: 未配置API密钥', 'error');
                return;
            }

            addLog(`开始发送消息: ${message}`, 'info');
            addLog(`使用${currentType === 'patient' ? '患者版' : '医生版'}API`, 'info');
            addLog(`API地址: ${apiUrl}`, 'info');
            addLog(`API密钥: ${apiKey.substring(0, 10)}...`, 'info');

            // 添加用户消息
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // 添加进度条
            const progressBar = createProgressBar();
            chatArea.appendChild(progressBar);
            chatArea.scrollTop = chatArea.scrollHeight;

            // 显示打字指示器
            showTypingIndicator();
            updateStatus('connecting', '正在发送消息...');
            updateProgress(10, '连接服务器...');

            isStreaming = true;
            sendBtn.disabled = true;

            try {
                updateProgress(20, '准备请求数据...');

                const requestBody = {
                    inputs: {},
                    query: message,
                    response_mode: 'streaming',
                    user: `user-${Date.now()}`
                };

                // 只有当conversation_id存在且有效时才添加
                if (currentConversationId && currentConversationId.trim()) {
                    requestBody.conversation_id = currentConversationId;
                    addLog(`使用现有对话ID: ${currentConversationId}`, 'info');
                } else {
                    addLog('开始新对话', 'info');
                }

                addLog(`请求体: ${JSON.stringify(requestBody, null, 2)}`, 'info');
                updateProgress(30, '发送请求...');

                let response = await fetch(`${apiUrl}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                addLog(`响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                updateProgress(50, '接收响应...');

                if (!response.ok) {
                    const errorText = await response.text();
                    addLog(`错误响应内容: ${errorText}`, 'error');

                    // 如果是对话不存在的错误，清除conversation_id并重试
                    if (response.status === 404 && errorText.includes('Conversation Not Exists')) {
                        addLog('对话已过期，清除对话ID并重试', 'warning');
                        currentConversationId = '';

                        // 重新发送请求，不包含conversation_id
                        const retryRequestBody = {
                            inputs: {},
                            query: message,
                            response_mode: 'streaming',
                            user: `user-${Date.now()}`
                        };

                        addLog('重新发送请求（新对话）', 'info');

                        response = await fetch(`${apiUrl}/chat-messages`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${apiKey}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(retryRequestBody)
                        });

                        if (!response.ok) {
                            const retryErrorText = await response.text();
                            addLog(`重试失败: ${retryErrorText}`, 'error');
                            throw new Error(`重试失败 HTTP ${response.status}: ${response.statusText}\n${retryErrorText}`);
                        }

                        addLog('重试成功，继续处理响应', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
                    }
                }

                // 移除打字指示器和进度条
                removeTypingIndicator();
                removeProgress();
                updateProgress(70, '处理流式数据...');

                // 创建流式消息容器
                currentStreamingMessage = addMessage('', 'ai', true);
                let fullResponse = '';
                let messageCount = 0;
                currentThinkingContent = '';
                currentMainContent = '';
                rawStreamContent = ''; // 重置原始内容累积器

                addLog('开始处理流式响应', 'info');

                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        addLog('流式响应结束', 'success');
                        break;
                    }

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                addLog(`收到事件: ${data.event}`, 'info');

                                // 处理不同类型的事件
                                if (data.event === 'message' || data.event === 'agent_message') {
                                    // 处理消息内容，累积完整内容后再分离思考和回答
                                    if (data.answer) {
                                        // 累积原始流式内容
                                        rawStreamContent += data.answer;
                                        addLog(`收到流式片段: ${data.answer}`, 'info');
                                        addLog(`当前累积内容长度: ${rawStreamContent.length}`, 'info');

                                        // 实时处理当前累积的内容
                                        const processed = processDeepSeekContent(rawStreamContent);

                                        // 更新显示内容
                                        currentThinkingContent = processed.thinking;
                                        currentMainContent = processed.main;

                                        if (processed.thinking) {
                                            addLog(`当前思考内容: ${processed.thinking.length}字符`, 'warning');
                                        }
                                        if (processed.main) {
                                            addLog(`当前主要内容: ${processed.main.length}字符`, 'success');
                                        }

                                        messageCount++;
                                        updateStreamingDisplay();
                                    }
                                } else if (data.event === 'agent_thought') {
                                    // 处理传统的思考事件（如果有的话）
                                    addLog(`收到思考事件，数据: ${JSON.stringify(data)}`, 'info');
                                    if (data.thought) {
                                        currentThinkingContent += data.thought;
                                        addLog(`AI思考内容: ${data.thought}`, 'warning');
                                        updateStreamingDisplay();
                                    }
                                } else if (data.event === 'message_end') {
                                    // 更新对话ID
                                    if (data.conversation_id) {
                                        currentConversationId = data.conversation_id;
                                        addLog(`更新对话ID: ${currentConversationId}`, 'info');
                                    }
                                    updateStatus('connected', '消息发送成功');

                                    if (data.metadata && data.metadata.usage) {
                                        addLog(`Token使用: ${JSON.stringify(data.metadata.usage)}`, 'info');
                                    }
                                } else if (data.event === 'error') {
                                    addLog(`流式错误: ${data.message}`, 'error');
                                    throw new Error(`流式错误: ${data.message}`);
                                }
                            } catch (e) {
                                addLog(`解析JSON失败: ${e.message}, 原始数据: ${line}`, 'warning');
                            }
                        }
                    }
                }

                addLog(`最终响应长度: 思考${currentThinkingContent.length}字符, 回答${currentMainContent.length}字符`, 'success');

            } catch (error) {
                removeTypingIndicator();
                removeProgress();
                addMessage(`抱歉，发生了错误：${error.message}`, 'ai');
                updateStatus('error', `错误：${error.message}`);
                addLog(`发送消息失败: ${error.message}`, 'error');
                console.error('发送消息失败:', error);
            } finally {
                isStreaming = false;
                sendBtn.disabled = false;
                currentStreamingMessage = null;
                currentThinkingContent = '';
                currentMainContent = '';
                rawStreamContent = '';
                addLog('消息发送流程结束', 'info');
            }
        }

        // 处理deepseek-r1模型的思考内容
        function processDeepSeekContent(text) {
            if (!text) return { thinking: '', main: '' };

            // 检查是否包含think标签
            const hasThinkTags = text.includes('<think>') || text.includes('</think>');

            if (!hasThinkTags) {
                // 没有think标签，全部作为主要内容
                return {
                    thinking: '',
                    main: text.trim()
                };
            }

            // 提取思考内容 <think>...</think>
            const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
            let thinkingContent = '';
            let match;

            // 提取所有完整的思考内容
            while ((match = thinkRegex.exec(text)) !== null) {
                thinkingContent += match[1];
            }

            // 移除完整的思考标签对，保留主要内容
            const mainContent = text.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

            // 如果有未闭合的<think>标签，暂时保留在主要内容中
            const hasUnclosedThink = text.includes('<think>') && !text.includes('</think>');

            return {
                thinking: thinkingContent.trim(),
                main: mainContent,
                hasUnclosedThink: hasUnclosedThink
            };
        }

        // 更新流式显示内容
        function updateStreamingDisplay() {
            if (!currentStreamingMessage) return;

            let displayContent = '';

            // 如果有思考内容，以小字号灰色显示
            if (currentThinkingContent.trim()) {
                displayContent += `<div class="thinking-content">💭 ${currentThinkingContent}</div>`;
                addLog(`显示思考内容: ${currentThinkingContent.length}字符`, 'info');
            }

            // 主要回答内容
            if (currentMainContent.trim()) {
                displayContent += `<div class="main-content-wrapper">
                    <pre>${currentMainContent}</pre>
                    <button class="speak-btn" data-text="${encodeURIComponent(currentMainContent)}">🔊 播放</button>
                </div>`;
            }

            currentStreamingMessage.innerHTML = displayContent;
            chatArea.scrollTop = chatArea.scrollHeight;

            // 调试：输出当前HTML内容
            console.log('当前显示内容:', displayContent);
        }

        // 测试函数：手动添加思考内容
        function testThinkingContent() {
            // 模拟deepseek-r1的输出格式
            const testContent = `<think>
让我分析一下麻醉的安全性问题。首先需要考虑现代麻醉技术的发展，以及各种监护设备的完善。我需要从多个角度来回答这个问题。
</think>

麻醉是非常安全的医疗程序。现代麻醉技术已经非常成熟，我们有完善的监护设备和专业的医疗团队来确保您的安全。

主要安全保障包括：
1. 术前详细评估
2. 实时生命体征监护
3. 专业麻醉医生全程陪护`;

            // 处理测试内容
            const processed = processDeepSeekContent(testContent);
            currentThinkingContent = processed.thinking;
            currentMainContent = processed.main;

            // 创建一个测试消息
            currentStreamingMessage = addMessage('', 'ai', true);
            updateStreamingDisplay();

            addLog('测试deepseek-r1格式内容已添加', 'info');
            addLog(`思考内容: ${processed.thinking}`, 'warning');
            addLog(`主要内容: ${processed.main}`, 'success');
        }

        // 测试流式思考内容
        function testStreamingThinking() {
            // 模拟流式数据片段
            const streamChunks = [
                '<think>\n让我分析一下',
                '麻醉的安全性问题。\n首先需要考虑现代',
                '麻醉技术的发展。\n</think>\n\n麻醉是',
                '非常安全的医疗程序。现代麻醉',
                '技术已经非常成熟。'
            ];

            // 重置状态
            rawStreamContent = '';
            currentThinkingContent = '';
            currentMainContent = '';
            currentStreamingMessage = addMessage('', 'ai', true);

            addLog('开始模拟流式思考内容', 'info');

            // 模拟流式处理
            streamChunks.forEach((chunk, index) => {
                setTimeout(() => {
                    rawStreamContent += chunk;
                    addLog(`流式片段 ${index + 1}: ${chunk}`, 'info');

                    const processed = processDeepSeekContent(rawStreamContent);
                    currentThinkingContent = processed.thinking;
                    currentMainContent = processed.main;

                    addLog(`当前思考: ${processed.thinking.length}字符`, 'warning');
                    addLog(`当前回答: ${processed.main.length}字符`, 'success');

                    updateStreamingDisplay();
                }, index * 500); // 每500ms一个片段
            });
        }

        // 配置管理功能
        function exportConfig() {
            const config = {
                apiUrl: document.getElementById('apiUrl').value,
                patientApiKey: document.getElementById('patientApiKey').value,
                doctorApiKey: document.getElementById('doctorApiKey').value,
                exportTime: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `麻醉智能体配置_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();

            addLog('配置已导出到文件', 'success');
        }

        function importConfig() {
            document.getElementById('configFileInput').click();
        }

        function handleConfigImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const config = JSON.parse(e.target.result);

                    // 验证配置格式
                    if (!config.apiUrl || !config.patientApiKey || !config.doctorApiKey) {
                        throw new Error('配置文件格式不正确');
                    }

                    // 应用配置
                    document.getElementById('apiUrl').value = config.apiUrl;
                    document.getElementById('patientApiKey').value = config.patientApiKey;
                    document.getElementById('doctorApiKey').value = config.doctorApiKey;

                    // 保存到localStorage
                    localStorage.setItem('apiUrl', config.apiUrl);
                    localStorage.setItem('patientApiKey', config.patientApiKey);
                    localStorage.setItem('doctorApiKey', config.doctorApiKey);

                    addLog(`配置已导入 (导出时间: ${config.exportTime || '未知'})`, 'success');
                    alert('配置导入成功！');
                } catch (error) {
                    addLog(`配置导入失败: ${error.message}`, 'error');
                    alert('配置文件格式错误，请检查文件内容');
                }
            };
            reader.readAsText(file);
        }

        // 语音转文字功能
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    isRecording = true;
                    voiceBtn.classList.add('recording');
                    voiceBtn.textContent = '🔴';
                    voiceBtn.title = '正在录音...点击停止';
                    addLog('开始语音识别', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    messageInput.value = transcript;
                    addLog(`语音识别结果: ${transcript}`, 'success');
                };

                recognition.onerror = function(event) {
                    addLog(`语音识别错误: ${event.error}`, 'error');
                    resetVoiceButton();
                };

                recognition.onend = function() {
                    resetVoiceButton();
                };

                return true;
            } else {
                addLog('浏览器不支持语音识别功能', 'error');
                voiceBtn.disabled = true;
                voiceBtn.title = '浏览器不支持语音识别';
                return false;
            }
        }

        function resetVoiceButton() {
            isRecording = false;
            voiceBtn.classList.remove('recording');
            voiceBtn.textContent = '🎤';
            voiceBtn.title = '语音输入';
        }

        // 智能选择最佳中文语音
        function findBestChineseVoice(voices) {
            // 优先级排序：更自然的语音优先
            const voicePriority = [
                // Windows 系统优质语音
                'Microsoft Yaoyao - Chinese (Simplified, PRC)',
                'Microsoft Kangkang - Chinese (Simplified, PRC)',
                'Microsoft Huihui - Chinese (Simplified, PRC)',
                'Microsoft Xiaoxiao - Chinese (Simplified, PRC)',

                // macOS 系统语音
                'Ting-Ting',
                'Sin-ji',
                'Mei-Jia',

                // Google Chrome 语音
                'Google 普通话（中国大陆）',
                'Google 中文（简体）',

                // 其他中文语音
                'Chinese Female',
                'Chinese Male',
                'zh-CN-XiaoxiaoNeural',
                'zh-CN-YunxiNeural',
                'zh-CN-YunyangNeural'
            ];

            // 按优先级查找
            for (const priorityName of voicePriority) {
                const voice = voices.find(v =>
                    v.name.includes(priorityName) ||
                    v.name === priorityName
                );
                if (voice) {
                    return voice;
                }
            }

            // 如果没找到优先语音，查找任何中文语音
            const chineseVoices = voices.filter(voice =>
                voice.lang.includes('zh') ||
                voice.name.includes('Chinese') ||
                voice.name.includes('中文') ||
                voice.name.includes('普通话')
            );

            if (chineseVoices.length > 0) {
                // 优先选择女声（通常更温和）
                const femaleVoice = chineseVoices.find(v =>
                    v.name.includes('Female') ||
                    v.name.includes('女') ||
                    v.name.includes('Xiaoxiao') ||
                    v.name.includes('Yaoyao') ||
                    v.name.includes('Huihui') ||
                    v.name.includes('Ting-Ting')
                );

                return femaleVoice || chineseVoices[0];
            }

            return null;
        }

        // 智能文本处理，让语音更自然
        function processTextForSpeech(text) {
            let processedText = text;

            // 1. 移除特殊字符，保留重要标点
            processedText = processedText.replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s，。！？；：""''（）【】、％%]/g, '');

            // 2. 处理医学术语的读音优化
            const medicalTerms = {
                'mg/kg': '毫克每公斤',
                'ml/kg': '毫升每公斤',
                'μg': '微克',
                'mg': '毫克',
                'ml': '毫升',
                'kg': '公斤',
                'min': '分钟',
                'h': '小时',
                'MAC': 'MAC值',
                'ASA': 'ASA分级',
                'ICU': 'ICU',
                'ECG': '心电图',
                'BP': '血压',
                'HR': '心率',
                'SpO2': '血氧饱和度'
            };

            for (const [term, pronunciation] of Object.entries(medicalTerms)) {
                const regex = new RegExp(term, 'gi');
                processedText = processedText.replace(regex, pronunciation);
            }

            // 3. 优化停顿和语调
            processedText = processedText
                .replace(/([。！？])\s*/g, '$1，，') // 句号后加长停顿
                .replace(/([，；])\s*/g, '$1，')     // 逗号后加短停顿
                .replace(/：/g, '，')               // 冒号改为逗号停顿
                .replace(/\s+/g, ' ')              // 合并空格
                .replace(/，{3,}/g, '，，')         // 限制停顿长度
                .trim();

            // 4. 处理数字读音
            processedText = processedText.replace(/(\d+)%/g, '百分之$1');
            processedText = processedText.replace(/(\d+)\.(\d+)/g, '$1点$2');

            // 5. 移除引用标记，让语音更流畅
            processedText = processedText.replace(/【[^】]*】/g, '');
            processedText = processedText.replace(/依据原文/g, '根据资料显示');

            return processedText;
        }

        function toggleVoiceRecognition() {
            if (!recognition) {
                if (!initSpeechRecognition()) {
                    return;
                }
            }

            if (isRecording) {
                recognition.stop();
                addLog('停止语音识别', 'info');
            } else {
                recognition.start();
            }
        }

        // 语音播放功能
        function speakText(text) {
            // 停止当前播放
            if (currentUtterance) {
                speechSynthesis.cancel();
            }

            // 智能文本处理，让语音更自然
            const cleanText = processTextForSpeech(text);

            if (!cleanText) {
                addLog('没有可播放的文本内容', 'warning');
                return;
            }

            currentUtterance = new SpeechSynthesisUtterance(cleanText);
            currentUtterance.lang = 'zh-CN';

            // 查找最佳中文语音
            const voices = speechSynthesis.getVoices();
            const bestVoice = findBestChineseVoice(voices);
            if (bestVoice) {
                currentUtterance.voice = bestVoice;
                addLog(`使用语音: ${bestVoice.name} (${bestVoice.lang})`, 'info');
            }

            // 根据用户类型调整语音参数
            if (currentType === 'patient') {
                // 患者版：温和、慢一点、音调稍低
                currentUtterance.rate = 0.85;
                currentUtterance.pitch = 0.9;
                currentUtterance.volume = 0.9;
            } else {
                // 医生版：专业、正常语速、标准音调
                currentUtterance.rate = 0.9;
                currentUtterance.pitch = 1.0;
                currentUtterance.volume = 1.0;
            }

            currentUtterance.onstart = function() {
                addLog(`开始播放语音: ${cleanText.substring(0, 30)}...`, 'info');
                // 更新所有播放按钮状态
                document.querySelectorAll('.speak-btn').forEach(btn => {
                    btn.classList.add('speaking');
                    btn.textContent = '⏸️ 停止';
                });
            };

            currentUtterance.onend = function() {
                addLog('语音播放完成', 'success');
                resetSpeakButtons();
            };

            currentUtterance.onerror = function(event) {
                addLog(`语音播放错误: ${event.error}`, 'error');
                resetSpeakButtons();
            };

            speechSynthesis.speak(currentUtterance);
        }

        function resetSpeakButtons() {
            document.querySelectorAll('.speak-btn').forEach(btn => {
                btn.classList.remove('speaking');
                btn.textContent = '🔊 播放';
            });
            currentUtterance = null;
        }

        function stopSpeaking() {
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                resetSpeakButtons();
                addLog('停止语音播放', 'info');
            }
        }

        // 测试语音功能
        function testVoice() {
            const testTexts = {
                patient: "您好，我是您的麻醉医生。麻醉是一个非常安全的医疗程序，现代麻醉技术已经非常成熟，请您放心。",
                doctor: "根据最新的临床指南，现代麻醉技术的安全性已经得到了显著提升，并发症发生率极低。"
            };

            const testText = testTexts[currentType];
            addLog(`测试${currentType === 'patient' ? '患者版' : '医生版'}语音`, 'info');

            // 显示可用语音列表
            const voices = speechSynthesis.getVoices();
            const chineseVoices = voices.filter(voice =>
                voice.lang.includes('zh') ||
                voice.name.includes('Chinese') ||
                voice.name.includes('中文') ||
                voice.name.includes('普通话')
            );

            addLog(`找到 ${chineseVoices.length} 个中文语音:`, 'info');
            chineseVoices.forEach((voice, index) => {
                addLog(`${index + 1}. ${voice.name} (${voice.lang})`, 'info');
            });

            const bestVoice = findBestChineseVoice(voices);
            if (bestVoice) {
                addLog(`推荐语音: ${bestVoice.name}`, 'success');
            }

            speakText(testText);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            init();

            // 初始化语音识别
            initSpeechRecognition();

            // 绑定语音按钮事件
            voiceBtn.addEventListener('click', toggleVoiceRecognition);

            // 加载语音列表
            if (speechSynthesis.onvoiceschanged !== undefined) {
                speechSynthesis.onvoiceschanged = function() {
                    const voices = speechSynthesis.getVoices();
                    addLog(`加载了 ${voices.length} 个语音`, 'info');
                };
            }

            // 点击播放按钮时的处理
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('speak-btn')) {
                    if (e.target.classList.contains('speaking')) {
                        stopSpeaking();
                    } else {
                        const text = decodeURIComponent(e.target.getAttribute('data-text'));
                        speakText(text);
                    }
                }
            });
        });
    </script>
</body>
</html>
