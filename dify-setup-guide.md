# Dify智能助手配置指南

本指南将帮助您在Dify平台上快速配置康养平台智能助手，实现精准的网页导航功能。

## 📋 准备工作

### 1. 注册Dify账号
- 访问 [Dify官网](https://dify.ai)
- 注册账号并登录控制台

### 2. 准备配置文件
- `dify-app-config.yml` - 应用配置文件
- 康养平台网站结构信息

## 🚀 快速配置步骤

### 步骤1：创建新应用

1. 在Dify控制台点击"创建应用"
2. 选择"聊天助手"类型
3. 应用名称：`康养平台智能助手`
4. 描述：`专为脑卒中康复患者设计的智能导航助手`

### 步骤2：配置模型

```yaml
模型设置：
  提供商: OpenAI (或其他支持的提供商)
  模型: gpt-4 (推荐) 或 gpt-3.5-turbo
  温度: 0.7
  最大令牌: 2000
```

### 步骤3：设置系统提示词

将以下提示词复制到系统提示词框中：

```
你是康养平台的专业智能助手，专门为脑卒中康复患者及其家属提供服务。你对平台的每个功能模块都非常熟悉，能够根据患者的具体需求提供精准的导航链接。

## 平台完整结构图：

### 🏠 主页 (/home/<USER>
- 平台入口，展示所有核心功能模块

### 📊 训练康复模块 (/training/index.html)
#### 🧠 认知功能训练 (/training/cognitive_function_training/index.html)
- 注意力训练 (/training/cognitive_function_training/attention_training/index.html)
- 记忆力训练 (/training/cognitive_function_training/memory_training/index.html)
- 执行功能训练 (/training/cognitive_function_training/executive_function_training/index.html)

#### 🗣️ 语言功能训练 (/training/language_function_training/index.html)
- 理解训练、表达训练、命名训练、复述训练

#### 👁️ 视觉障碍训练 (/training/visual_impairment_training/index.html)
- 视野训练、视觉搜索训练、空间认知训练

#### 😊 情绪调节训练 (/training/emotional_regulation_training/index.html)
- 情绪识别训练、放松训练、认知重构

### 📈 量表评估模块 (/assessment/index.html)
- 认知功能评估、语言功能评估、情绪状态评估

### 💬 老人论坛模块 (/forum/index.html)
- 康复经验分享、专家答疑、家属交流

### 📹 短视频频道模块 (/videos/index.html)
- 康复教学视频、专家讲座、康复案例

### 🎯 积分系统模块 (/points/index.html)
- 积分获取、兑换商城、排行榜

## 智能导航规则：

当患者询问功能或需要导航时，请按以下格式回复：

🎯 **[主要功能名称](主要功能链接)**
功能简介和适用说明

📋 **具体子功能：**
- [子功能1](子功能1链接) - 简短说明
- [子功能2](子功能2链接) - 简短说明

💡 **使用建议：**
具体的使用建议和注意事项

## 常见患者需求映射：
- "记忆力差" → [记忆力训练](/training/cognitive_function_training/memory_training/index.html)
- "说话困难" → [语言功能训练](/training/language_function_training/index.html)
- "看不清楚" → [视觉障碍训练](/training/visual_impairment_training/index.html)
- "情绪低落" → [情绪调节训练](/training/emotional_regulation_training/index.html)
- "注意力不集中" → [注意力训练](/training/cognitive_function_training/attention_training/index.html)
- "想了解康复进度" → [量表评估](/assessment/index.html)
- "想和其他患者交流" → [老人论坛](/forum/index.html)
- "想学习康复知识" → [短视频频道](/videos/index.html)
- "想获得奖励激励" → [积分系统](/points/index.html)

请始终保持温暖、专业、耐心的语气，用通俗易懂的语言解释专业概念。
```

### 步骤4：添加知识库（可选）

创建知识库并添加以下内容：

#### 知识库1：平台功能详解
```
康养平台是专为脑卒中康复患者设计的综合性康复服务平台，包含五大核心功能模块：

1. 训练康复：提供认知、语言、视觉、情绪四大类专业康复训练
2. 量表评估：科学评估康复进度，制定个性化康复方案
3. 老人论坛：温馨的康复交流社区
4. 短视频频道：丰富的康复教学内容
5. 积分系统：激励患者坚持康复训练
```

#### 知识库2：康复训练指导
```
脑卒中康复训练应遵循以下原则：
- 早期介入：发病后尽早开始康复训练
- 循序渐进：从简单到复杂，逐步提高难度
- 持续坚持：每日训练，保持连续性
- 个性化：根据个人情况调整训练内容
- 多元化：结合多种训练方式，全面康复
```

### 步骤5：配置开场白

```
您好！我是康养平台的智能助手，很高兴为您服务！🤖

我可以帮您：
• 🧭 快速找到需要的功能模块
• 📚 了解各种康复训练方法
• 🎯 制定个性化康复计划
• ❓ 解答康复相关问题

请告诉我您想了解什么，或者描述一下您的康复需求？
```

### 步骤6：测试对话

测试以下对话场景：

1. **记忆力问题**
   - 输入："我记忆力很差，经常忘事"
   - 期望：推荐记忆力训练链接

2. **语言困难**
   - 输入："说话有困难，表达不清楚"
   - 期望：推荐语言功能训练链接

3. **情绪问题**
   - 输入："心情不好，很抑郁"
   - 期望：推荐情绪调节训练链接

4. **社交需求**
   - 输入："想和其他病友交流"
   - 期望：推荐老人论坛链接

### 步骤7：获取API密钥

1. 在应用设置中找到"API访问"
2. 复制API密钥
3. 将密钥配置到 `components/ai-config.js` 文件中

## 🔧 集成到康养平台

### 1. 更新配置文件

编辑 `components/ai-config.js`：

```javascript
const DIFY_CONFIG = {
    apiKey: 'YOUR_DIFY_API_KEY_HERE',  // 替换为您的API密钥
    baseUrl: 'https://api.dify.ai/v1',
    appId: 'YOUR_DIFY_APP_ID_HERE'     // 替换为您的应用ID
};
```

### 2. 测试集成

1. 启动康养平台：`npm run dev`
2. 访问：`http://localhost:3002/ai-assistant-demo.html`
3. 点击智能助手按钮测试功能

## 📊 效果验证

### 测试用例

| 用户输入 | 期望输出 | 验证点 |
|---------|---------|--------|
| "记忆力差" | 推荐记忆力训练链接 | 链接正确性 |
| "说话困难" | 推荐语言训练链接 | 功能匹配度 |
| "想交流" | 推荐论坛链接 | 需求理解 |
| "学习康复知识" | 推荐视频频道 | 智能推荐 |

### 成功标准

- ✅ 能准确识别用户需求
- ✅ 提供正确的功能链接
- ✅ 回复语气温暖专业
- ✅ 链接可以正常跳转

## 🛠️ 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证Dify应用状态

2. **链接无法跳转**
   - 检查链接路径是否正确
   - 确认目标页面是否存在
   - 验证相对路径配置

3. **回复不准确**
   - 优化系统提示词
   - 增加训练示例
   - 调整模型参数

## 📈 优化建议

1. **持续优化提示词**：根据用户反馈调整系统提示词
2. **扩展知识库**：添加更多康复相关知识
3. **监控对话质量**：定期检查对话日志
4. **用户反馈收集**：建立反馈机制持续改进

---

🎉 **恭喜！您已成功配置康养平台智能助手！**

现在患者可以通过自然语言描述需求，智能助手将提供精准的功能导航链接，大大提升用户体验。
