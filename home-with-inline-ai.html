<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>康养平台 - 脑卒中康复服务平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="../components/ai-assistant.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-heartbeat"></i>
                <h1>康养平台</h1>
            </div>
            <nav class="nav">
                <a href="#" class="nav-link">首页</a>
                <a href="#" class="nav-link">关于我们</a>
                <a href="#" class="nav-link">联系我们</a>
            </nav>
        </header>

        <main class="main">
            <section class="hero">
                <div class="hero-content">
                    <h2>专业的脑卒中康复服务平台</h2>
                    <p>为脑卒中患者提供个性化康复训练、科学评估和全方位支持</p>
                    <button class="cta-button">开始康复之旅</button>
                </div>
            </section>

            <section class="features">
                <div class="feature-grid">
                    <div class="feature-card" onclick="navigateTo('/training/index.html')">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3>训练康复</h3>
                        <p>个性化康复训练方案，包含认知、语言、视觉、情绪四大训练模块</p>
                        <div class="feature-stats">
                            <span>4大训练模块</span>
                            <span>个性化方案</span>
                        </div>
                    </div>

                    <div class="feature-card" onclick="navigateTo('/assessment/index.html')">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>量表评估</h3>
                        <p>科学评估康复进度，制定精准康复方案</p>
                        <div class="feature-stats">
                            <span>科学评估</span>
                            <span>精准方案</span>
                        </div>
                    </div>

                    <div class="feature-card" onclick="navigateTo('/forum/index.html')">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>老人论坛</h3>
                        <p>康复经验交流，病友互助支持社区</p>
                        <div class="feature-stats">
                            <span>经验分享</span>
                            <span>互助支持</span>
                        </div>
                    </div>

                    <div class="feature-card" onclick="navigateTo('/videos/index.html')">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>短视频频道</h3>
                        <p>康复教学视频，专家指导内容</p>
                        <div class="feature-stats">
                            <span>专家指导</span>
                            <span>教学视频</span>
                        </div>
                    </div>

                    <div class="feature-card" onclick="navigateTo('/points/index.html')">
                        <div class="feature-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3>积分系统</h3>
                        <p>完成训练获得积分，兑换精美礼品</p>
                        <div class="feature-stats">
                            <span>积分奖励</span>
                            <span>礼品兑换</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- AI智能助手 -->
    <div id="aiAssistant" class="ai-assistant">
        <div id="aiAssistantToggle" class="ai-toggle-btn">
            <i class="fas fa-robot"></i>
            <span class="ai-pulse"></span>
        </div>
        
        <div id="aiChatWindow" class="ai-chat-window">
            <div class="ai-chat-header">
                <div class="ai-header-info">
                    <i class="fas fa-robot"></i>
                    <span>康养助手</span>
                </div>
                <div class="ai-header-actions">
                    <button id="aiCloseBtn" class="ai-btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="ai-chat-content">
                <div id="aiChatMessages" class="ai-chat-messages">
                    <div class="ai-message ai-message-bot">
                        <div class="ai-message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-message-content">
                            <div class="ai-message-text">
                                您好！我是康养平台智能助手 🤖<br><br>
                                我可以帮您：<br>
                                • 🧭 快速找到康复功能<br>
                                • 📚 了解训练方法<br>
                                • 🎯 制定康复计划<br>
                                • ❓ 解答相关问题<br><br>
                                请问有什么可以帮助您的吗？
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="ai-quick-actions">
                    <button class="ai-quick-btn" data-action="training">训练康复</button>
                    <button class="ai-quick-btn" data-action="assessment">量表评估</button>
                    <button class="ai-quick-btn" data-action="forum">老人论坛</button>
                    <button class="ai-quick-btn" data-action="videos">短视频</button>
                </div>
            </div>
            
            <div class="ai-chat-input">
                <div class="ai-input-container">
                    <textarea 
                        id="aiMessageInput" 
                        class="ai-input-field" 
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <button id="aiSendBtn" class="ai-send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    
    <!-- 内联AI助手配置和脚本 -->
    <script>
        // Dify API配置 - 本地部署版本
        window.DIFY_CONFIG = {
            apiKey: 'app-mkGNIAGwOq1qcW2toI6IruXZ',
            baseUrl: 'http://127.0.0.1/v1',
            appId: 'vSE7UiS3QeR76ycy',
            user: 'kangyang-platform-user'
        };

        // 简化的AI助手类
        class SimpleAIAssistant {
            constructor() {
                this.isOpen = false;
                this.conversationId = null;
                this.init();
            }

            init() {
                console.log('初始化AI助手...');
                this.bindEvents();
            }

            bindEvents() {
                const toggleBtn = document.getElementById('aiAssistantToggle');
                const closeBtn = document.getElementById('aiCloseBtn');
                const sendBtn = document.getElementById('aiSendBtn');
                const messageInput = document.getElementById('aiMessageInput');

                if (toggleBtn) {
                    toggleBtn.addEventListener('click', () => this.toggleChat());
                }

                if (closeBtn) {
                    closeBtn.addEventListener('click', () => this.closeChat());
                }

                if (sendBtn) {
                    sendBtn.addEventListener('click', () => this.sendMessage());
                }

                if (messageInput) {
                    messageInput.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            this.sendMessage();
                        }
                    });
                }

                // 快捷按钮
                document.querySelectorAll('.ai-quick-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const action = btn.getAttribute('data-action');
                        this.handleQuickAction(action);
                    });
                });
            }

            toggleChat() {
                const chatWindow = document.getElementById('aiChatWindow');
                this.isOpen = !this.isOpen;
                chatWindow.classList.toggle('active', this.isOpen);
            }

            closeChat() {
                const chatWindow = document.getElementById('aiChatWindow');
                this.isOpen = false;
                chatWindow.classList.remove('active');
            }

            async sendMessage() {
                const messageInput = document.getElementById('aiMessageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;
                
                messageInput.value = '';
                this.addMessage(message, 'user');
                
                try {
                    const response = await this.callDifyAPI(message);
                    this.addMessage(response, 'bot');
                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.addMessage('抱歉，我现在无法回复您的消息。请稍后再试。', 'bot');
                }
            }

            addMessage(content, type) {
                const messagesContainer = document.getElementById('aiChatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `ai-message ai-message-${type}`;
                
                if (type === 'user') {
                    messageDiv.innerHTML = `
                        <div class="ai-message-content ai-message-user">
                            <div class="ai-message-text">${this.escapeHtml(content)}</div>
                        </div>
                        <div class="ai-message-avatar ai-user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="ai-message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-message-content">
                            <div class="ai-message-text">${this.processMessage(content)}</div>
                        </div>
                    `;
                }
                
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            processMessage(content) {
                return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="ai-link">$1</a>');
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            async callDifyAPI(message) {
                const response = await fetch(`${window.DIFY_CONFIG.baseUrl}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${window.DIFY_CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: message,
                        user: window.DIFY_CONFIG.user,
                        conversation_id: this.conversationId,
                        response_mode: 'blocking'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.conversation_id) {
                    this.conversationId = data.conversation_id;
                }
                
                return data.answer || '抱歉，我没有理解您的问题。';
            }

            handleQuickAction(action) {
                const actionMap = {
                    'training': '我想了解训练康复功能',
                    'assessment': '我想进行量表评估',
                    'forum': '我想去老人论坛交流',
                    'videos': '我想观看康复视频'
                };
                
                const message = actionMap[action];
                if (message) {
                    const messageInput = document.getElementById('aiMessageInput');
                    messageInput.value = message;
                    this.sendMessage();
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化简化版AI助手...');
            window.aiAssistant = new SimpleAIAssistant();
            console.log('AI助手初始化完成');
        });
    </script>
</body>
</html>
