<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言功能训练 - 康养平台</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 16px;
            line-height: 1.8;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .training-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #17a2b8;
        }
        .training-item h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .training-item p {
            color: #666;
            margin-bottom: 8px;
        }
        .zoom-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .zoom-btn:hover {
            background: #5a67d8;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 放大缩小控制 -->
    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">
            <i class="fas fa-plus"></i>
        </button>
        <button class="zoom-btn" onclick="zoomOut()">
            <i class="fas fa-minus"></i>
        </button>
        <button class="zoom-btn" onclick="resetZoom()">
            <i class="fas fa-undo"></i>
        </button>
    </div>

    <div class="container">
        <a href="/training/index.html" class="back-link">← 返回训练康复</a>
        
        <div class="header">
            <h1>🗣️ 语言功能训练</h1>
            <p>改善语言表达和理解能力</p>
        </div>
        
        <div class="training-item">
            <h3>👂 理解训练</h3>
            <p>提升语言理解能力</p>
            <p>• 词汇理解练习</p>
            <p>• 句子理解训练</p>
            <p>• 语篇理解练习</p>
            <p>• 指令理解训练</p>
        </div>
        
        <div class="training-item">
            <h3>💬 表达训练</h3>
            <p>改善语言表达能力</p>
            <p>• 命名练习</p>
            <p>• 复述训练</p>
            <p>• 描述练习</p>
            <p>• 对话训练</p>
        </div>
        
        <div class="training-item">
            <h3>📖 读写训练</h3>
            <p>恢复读写能力</p>
            <p>• 字词识别</p>
            <p>• 阅读理解</p>
            <p>• 书写练习</p>
            <p>• 拼写训练</p>
        </div>
        
        <div class="training-item">
            <h3>🎵 语音训练</h3>
            <p>改善发音和语调</p>
            <p>• 发音练习</p>
            <p>• 语调训练</p>
            <p>• 语速控制</p>
            <p>• 清晰度提升</p>
        </div>
    </div>

    <script>
        let currentZoom = 100;
        
        function zoomIn() {
            currentZoom += 10;
            if (currentZoom > 150) currentZoom = 150;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function zoomOut() {
            currentZoom -= 10;
            if (currentZoom < 80) currentZoom = 80;
            document.body.style.zoom = currentZoom + '%';
        }
        
        function resetZoom() {
            currentZoom = 100;
            document.body.style.zoom = '100%';
        }
    </script>

    <!-- AI智能助手 -->
    <script src="../../components/load-assistant.js"></script>
</body>
</html>
