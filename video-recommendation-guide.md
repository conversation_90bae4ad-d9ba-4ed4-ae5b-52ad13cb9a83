# 视频推送功能使用指南

## 功能概述

康养平台智能体现已集成视频推送功能，能够根据用户的问题智能推荐相关的康复视频链接（主要来自抖音等平台），并提供详细的视频内容说明。

## 主要特性

### 🎯 智能推荐算法
- **关键词匹配**：基于用户问题中的关键词进行精准匹配
- **语义分析**：理解用户的康复需求，推荐最相关的视频
- **分类推荐**：按照康复训练类型进行分类推荐
- **热门排序**：当没有精确匹配时，推荐热门康复视频

### 📚 丰富的视频数据库
包含以下6大分类的康复视频：

1. **🧠 认知功能训练**
   - 记忆力训练基础教程
   - 注意力集中训练法
   - 执行功能康复指导

2. **🗣️ 语言功能训练**
   - 失语症康复训练方法
   - 发音练习基础教程
   - 阅读理解康复训练

3. **😊 情绪调节训练**
   - 康复期情绪调节技巧
   - 放松训练指导
   - 积极心态建立方法

4. **🏃‍♂️ 运动康复训练**
   - 床上康复训练动作
   - 平衡训练基础教程
   - 手功能康复训练

5. **🏠 日常生活训练**
   - 日常生活能力训练
   - 家庭康复环境布置

6. **🌟 成功案例分享**
   - 康复成功案例分享
   - 康复路上的坚持

### 🎬 视频信息详细
每个推荐视频包含：
- **标题**：简洁明了的视频标题
- **描述**：详细的内容说明和适用人群
- **时长**：视频播放时长
- **观看数**：视频热度指标
- **平台**：视频来源平台（抖音等）
- **直链**：可直接点击观看的链接

## 使用方法

### 1. 通过AI智能助手
在任何页面点击右下角的AI助手图标，输入康复相关问题：

**示例问题：**
- "我记忆力不好，有什么训练方法吗？"
- "说话不清楚怎么办？"
- "心情不好，如何调节情绪？"
- "手功能训练有什么视频教程？"
- "有没有成功康复的案例？"

### 2. 访问视频频道页面
直接访问 `/videos/index.html` 页面，浏览所有分类的康复视频。

### 3. 测试页面验证
使用 `video-recommendation-test.html` 页面测试推荐功能的准确性。

## 技术实现

### 核心组件

1. **视频数据库** (`AI_KNOWLEDGE_BASE.videoDatabase`)
   - 结构化存储所有康复视频信息
   - 支持多平台视频链接
   - 包含详细的元数据信息

2. **关键词映射** (`AI_KNOWLEDGE_BASE.videoKeywordMapping`)
   - 将用户输入的关键词映射到视频分类
   - 支持同义词和相关词匹配
   - 覆盖康复训练的各个方面

3. **推荐引擎** (`VideoRecommendationEngine`)
   - 智能分析用户问题
   - 计算匹配分数
   - 生成个性化推荐

4. **响应格式化** (`formatVideoResponse`)
   - 美化推荐结果展示
   - 提供快速访问链接
   - 包含使用建议和注意事项

### 集成方式

视频推送功能已完全集成到现有的AI智能助手中：

- **无缝集成**：不影响原有功能
- **智能判断**：自动识别是否需要视频推荐
- **优雅降级**：推荐失败时提供备选方案
- **用户友好**：提供清晰的使用指导

## 使用建议

### 对用户
1. **描述具体问题**：越具体的问题，推荐越精准
2. **使用关键词**：包含"记忆"、"语言"、"情绪"等关键词
3. **关注安全提示**：遵循视频中的安全指导
4. **循序渐进**：从简单的训练开始

### 对开发者
1. **扩展视频库**：定期添加新的康复视频
2. **优化关键词**：根据用户反馈调整关键词映射
3. **监控效果**：跟踪推荐准确率和用户满意度
4. **更新链接**：确保视频链接的有效性

## 注意事项

1. **专业指导**：建议在专业人士指导下进行康复训练
2. **个体差异**：根据个人情况调整训练强度
3. **安全第一**：如有不适请及时停止并咨询医生
4. **持续性**：康复是一个长期过程，需要坚持

## 未来优化方向

1. **个性化推荐**：基于用户历史记录提供更精准推荐
2. **视频评分**：添加用户评分和反馈系统
3. **离线缓存**：支持视频信息的本地缓存
4. **多平台支持**：扩展到更多视频平台
5. **AI增强**：使用更先进的AI算法提升推荐质量

---

**版本信息**
- 创建日期：2025-07-31
- 版本：v1.0
- 作者：康养平台开发团队
