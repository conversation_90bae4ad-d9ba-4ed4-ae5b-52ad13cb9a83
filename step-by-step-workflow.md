# 康养平台智能体工作流搭建教程
## 🎯 手把手教学，每步都可测试

---

## 📋 准备工作

### 必需材料
- ✅ `kangyang-agent.yml` - 智能体配置文件（已生成）
- ✅ 康养平台项目（端口3002运行中）
- ✅ 现代浏览器（Chrome/Firefox推荐）
- ✅ 稳定的网络连接

### 预计时间
- 总时长：约30-45分钟
- 每步骤：3-8分钟
- 测试验证：每步2-3分钟

---

## 🚀 第一步：Dify账号准备与应用创建

### 1.1 注册Dify账号
```
🔗 访问：https://dify.ai
📝 点击"Sign Up"注册账号
✉️ 使用邮箱验证激活账号
🔑 登录Dify控制台
```

**✅ 测试验证**：
- 能够成功登录Dify控制台
- 看到应用管理界面

### 1.2 创建智能助手应用
```
1. 点击"创建应用"按钮
2. 选择"聊天助手"类型
3. 应用名称：康养平台智能助手
4. 描述：专为脑卒中康复患者设计的智能导航助手
5. 点击"创建"
```

**✅ 测试验证**：
- 应用创建成功
- 进入应用配置界面
- 获得应用ID（记录下来）

### 1.3 导入YAML配置（可选）
```
💡 提示：可以手动配置，也可以尝试导入YAML
📁 如果支持导入，选择 kangyang-agent.yml 文件
⚙️ 如果不支持，继续手动配置（推荐）
```

**✅ 测试验证**：
- 配置界面正常显示
- 准备进入下一步配置

---

## 🔧 第二步：基础配置与测试

### 2.1 配置模型参数
```
🤖 模型设置：
   - 提供商：OpenAI（或其他可用提供商）
   - 模型：GPT-4（推荐）或 GPT-3.5-turbo
   - 温度：0.7
   - 最大令牌：2000
   - Top P：1.0
```

**✅ 测试验证**：
- 模型配置保存成功
- 参数显示正确

### 2.2 设置系统提示词
```
📝 复制以下提示词到系统设置：

你是康养平台的专业智能助手，专门为脑卒中康复患者及其家属提供温暖、专业的服务。

## 🏥 康养平台功能架构

### 📊 训练康复模块
- 认知功能训练: /training/cognitive_function_training/index.html
  * 记忆力训练: /training/cognitive_function_training/memory_training/index.html
  * 注意力训练: /training/cognitive_function_training/attention_training/index.html
- 语言功能训练: /training/language_function_training/index.html
- 视觉障碍训练: /training/visual_impairment_training/index.html
- 情绪调节训练: /training/emotional_regulation_training/index.html

### 📈 其他核心模块
- 量表评估: /assessment/index.html
- 老人论坛: /forum/index.html
- 短视频频道: /videos/index.html
- 积分系统: /points/index.html

## 🎯 回复格式
当用户描述症状时，请按以下格式回复：

🎯 **[功能名称](功能链接)**
功能简介

📋 **推荐训练：**
- [具体训练](链接) - 说明

💡 **使用建议：**
具体指导

🌟 康复是循序渐进的过程，请保持信心！

## 症状映射
- 记忆问题 → 记忆力训练
- 说话困难 → 语言功能训练  
- 视觉问题 → 视觉障碍训练
- 情绪低落 → 情绪调节训练

请用温暖、专业的语气回答，提供可点击的功能链接。
```

**✅ 测试验证**：
- 提示词保存成功
- 字符数在限制范围内

### 2.3 设置开场白
```
📝 开场白内容：

您好！我是康养平台的智能助手，很高兴为您服务！🤖

我可以帮您：
• 🧭 快速找到需要的康复功能
• 📚 了解各种训练方法
• 🎯 制定个性化康复计划
• ❓ 解答康复相关问题

请告诉我您的康复需求，或者描述一下遇到的困难？
```

**✅ 测试验证**：
- 开场白显示正确
- 语气温暖专业

### 2.4 基础对话测试
```
🧪 在Dify测试界面输入：
"你好"

🎯 期望回复：
- 友好的问候
- 功能介绍
- 引导用户描述需求
```

**✅ 测试验证**：
- 回复内容合适
- 语气符合预期
- 没有错误信息

---

## 📚 第三步：知识库配置

### 3.1 创建知识库
```
📁 在Dify中创建知识库：
   - 名称：康养平台知识库
   - 描述：脑卒中康复训练相关知识
   - 类型：文档知识库
```

### 3.2 上传知识文档
```
📄 创建并上传以下文档：

文档1：平台功能介绍
---
康养平台是专为脑卒中康复患者设计的综合性康复服务平台。

核心功能模块：
1. 训练康复 - 认知、语言、视觉、情绪四大训练
2. 量表评估 - 科学评估康复进度
3. 老人论坛 - 康复经验交流社区
4. 短视频频道 - 康复教学内容
5. 积分系统 - 激励康复训练

文档2：康复训练指导
---
脑卒中康复训练原则：
- 早期介入：发病后尽早开始
- 循序渐进：从简单到复杂
- 持续坚持：每日训练30-60分钟
- 个性化：根据个人情况调整
- 多元化：结合多种训练方式

认知功能训练包括：
- 记忆力训练：工作记忆、长期记忆训练
- 注意力训练：集中注意力、分散注意力训练
- 执行功能：计划、决策、问题解决能力训练
```

**✅ 测试验证**：
- 文档上传成功
- 知识库索引完成
- 可以搜索到相关内容

### 3.3 测试知识检索
```
🧪 测试输入：
"认知功能训练包括什么？"

🎯 期望回复：
- 包含知识库中的相关信息
- 提供具体的训练类型
- 给出功能链接
```

**✅ 测试验证**：
- 能够检索到知识库内容
- 回复准确且完整
- 链接格式正确

---

## 🔗 第四步：智能链接生成配置

### 4.1 症状识别测试
```
🧪 测试用例1：记忆问题
输入："我记忆力很差，经常忘事"
期望：推荐记忆力训练链接

🧪 测试用例2：语言困难  
输入："说话有困难，表达不清楚"
期望：推荐语言功能训练链接

🧪 测试用例3：情绪问题
输入："心情很低落，不想训练"
期望：推荐情绪调节训练链接

🧪 测试用例4：社交需求
输入："想和其他病友交流"
期望：推荐老人论坛链接
```

**✅ 测试验证**：
- 能准确识别用户症状
- 推荐正确的功能模块
- 链接格式规范
- 回复内容专业温暖

### 4.2 链接格式验证
```
✅ 检查回复是否包含：
- 🎯 功能标题和链接
- 📋 具体训练推荐
- 💡 使用建议
- 🌟 鼓励话语

✅ 检查链接格式：
- [功能名称](链接地址)
- 链接地址以 / 开头
- 路径正确无误
```

**✅ 测试验证**：
- 所有链接格式正确
- 可以点击跳转（后续测试）
- 内容结构清晰

---

## 🔌 第五步：API集成与前端连接

### 5.1 获取API信息
```
🔑 在Dify应用设置中：
1. 找到"API访问"或"API密钥"
2. 复制API Key（以app-开头）
3. 记录应用ID
4. 确认API端点地址
```

**✅ 测试验证**：
- API Key获取成功
- 应用ID记录正确
- 端点地址确认

### 5.2 更新前端配置
```
📝 编辑 components/ai-config.js：

const DIFY_CONFIG = {
    apiKey: 'app-你的实际API密钥',
    baseUrl: 'https://api.dify.ai/v1',
    appId: '你的实际应用ID',
    user: 'kangyang-platform-user'
};
```

**✅ 测试验证**：
- 配置文件更新成功
- 语法无错误
- 信息填写正确

### 5.3 API连接测试
```
🧪 在浏览器控制台测试：

// 打开 http://localhost:3002/home/<USER>
// 按F12打开控制台，输入：

fetch('https://api.dify.ai/v1/chat-messages', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer 你的API密钥',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        inputs: {},
        query: '你好',
        user: 'test-user',
        response_mode: 'blocking'
    })
}).then(response => response.json())
  .then(data => console.log('API测试结果:', data));
```

**✅ 测试验证**：
- API调用成功（状态码200）
- 返回正常响应数据
- 无网络错误

### 5.4 智能助手集成测试
```
🧪 测试步骤：
1. 访问：http://localhost:3002/home/<USER>
2. 点击智能助手悬浮按钮
3. 输入测试消息："你好"
4. 查看回复内容
```

**✅ 测试验证**：
- 智能助手正常显示
- 对话窗口正常打开
- API调用成功
- 回复内容正确

---

## 🧪 第六步：全面功能测试

### 6.1 运行自动化测试
```
🔗 访问测试框架：
http://localhost:3002/test-framework.html

🚀 点击"运行所有测试"按钮
📊 查看测试结果和日志
```

**✅ 测试验证**：
- 所有测试项目通过
- 无严重错误
- 性能指标正常

### 6.2 手动功能验证
```
🧪 完整对话测试：

测试1："我记忆力很差"
✅ 推荐记忆力训练
✅ 提供使用建议
✅ 链接格式正确

测试2："想学习康复知识"  
✅ 推荐短视频频道
✅ 内容描述准确
✅ 鼓励话语温暖

测试3："心情不好"
✅ 推荐情绪调节训练
✅ 提供心理支持
✅ 专业建议合理
```

**✅ 测试验证**：
- 所有对话测试通过
- 链接可以正确跳转
- 用户体验良好

---

## 🎯 第七步：优化与部署

### 7.1 性能优化
```
⚡ 检查响应速度：
- API调用 < 3秒
- 页面加载 < 2秒
- 交互响应 < 1秒

🎨 界面优化：
- 移动端适配良好
- 按钮大小合适
- 颜色搭配和谐
```

### 7.2 最终验证清单
```
✅ 基础功能
- [ ] 智能助手正常显示
- [ ] 对话功能完整
- [ ] API连接稳定

✅ 智能功能
- [ ] 症状识别准确
- [ ] 链接生成正确
- [ ] 回复内容专业

✅ 用户体验
- [ ] 界面友好美观
- [ ] 操作流程顺畅
- [ ] 错误处理完善
```

### 7.3 部署完成
```
🎉 恭喜！智能体工作流搭建完成！

📊 最终效果：
- 用户可以自然语言描述康复需求
- 智能体准确理解并推荐功能
- 一键跳转到相应的康复模块
- 获得专业温暖的康复指导

🔄 后续维护：
- 定期检查API状态
- 收集用户反馈优化
- 更新知识库内容
- 扩展功能边界
```

---

## 🆘 故障排除

### 常见问题快速解决
```
❌ 问题：智能助手不显示
✅ 解决：检查文件路径和网络连接

❌ 问题：API调用失败
✅ 解决：验证API Key和应用ID

❌ 问题：链接跳转异常  
✅ 解决：检查目标页面是否存在

❌ 问题：回复内容不准确
✅ 解决：优化系统提示词和知识库
```

---

🎊 **工作流搭建完成！现在您拥有了一个功能完整的康养平台智能体！**
