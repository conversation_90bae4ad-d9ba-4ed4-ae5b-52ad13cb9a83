<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短视频频道 - 康养平台</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .video-category {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #dc3545;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .video-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .video-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        }
        .video-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .video-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 12px;
            color: #888;
        }
        .video-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: opacity 0.2s;
        }
        .video-link:hover {
            opacity: 0.9;
        }
        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .category-title {
            font-size: 24px;
            color: #333;
            margin: 0;
        }
        .show-more-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .show-more-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/home/<USER>" class="back-link">← 返回主页</a>
        
        <div class="header">
            <h1>📹 短视频频道</h1>
            <p>康复教学视频，专家指导内容，智能推荐相关康复视频</p>
        </div>

        <!-- 认知功能训练视频 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">🧠 认知功能训练</h2>
                <button class="show-more-btn" onclick="toggleCategory('cognitive')">显示更多</button>
            </div>
            <div id="cognitive-videos" class="video-grid"></div>
        </div>

        <!-- 语言功能训练视频 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">🗣️ 语言功能训练</h2>
                <button class="show-more-btn" onclick="toggleCategory('language')">显示更多</button>
            </div>
            <div id="language-videos" class="video-grid"></div>
        </div>

        <!-- 情绪调节训练视频 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">😊 情绪调节训练</h2>
                <button class="show-more-btn" onclick="toggleCategory('emotional')">显示更多</button>
            </div>
            <div id="emotional-videos" class="video-grid"></div>
        </div>

        <!-- 运动康复训练视频 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">🏃‍♂️ 运动康复训练</h2>
                <button class="show-more-btn" onclick="toggleCategory('physical')">显示更多</button>
            </div>
            <div id="physical-videos" class="video-grid"></div>
        </div>

        <!-- 日常生活训练视频 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">🏠 日常生活训练</h2>
                <button class="show-more-btn" onclick="toggleCategory('daily')">显示更多</button>
            </div>
            <div id="daily-videos" class="video-grid"></div>
        </div>

        <!-- 成功案例分享 -->
        <div class="video-category">
            <div class="category-header">
                <h2 class="category-title">🌟 成功案例分享</h2>
                <button class="show-more-btn" onclick="toggleCategory('success_stories')">显示更多</button>
            </div>
            <div id="success_stories-videos" class="video-grid"></div>
        </div>
    </div>

    <!-- 视频页面脚本 -->
    <script>
        // 视频显示状态
        const categoryStates = {
            cognitive: { expanded: false, showCount: 2 },
            language: { expanded: false, showCount: 2 },
            emotional: { expanded: false, showCount: 2 },
            physical: { expanded: false, showCount: 2 },
            daily: { expanded: false, showCount: 2 },
            success_stories: { expanded: false, showCount: 2 }
        };

        // 创建视频卡片HTML
        function createVideoCard(video) {
            return `
                <div class="video-card">
                    <div class="video-title">${video.title}</div>
                    <div class="video-description">${video.description}</div>
                    <div class="video-meta">
                        <span>⏱️ ${video.duration}</span>
                        <span>🔥 ${video.views}次观看</span>
                        <span>📱 ${video.platform}</span>
                    </div>
                    <a href="${video.url}" target="_blank" class="video-link">
                        观看视频 →
                    </a>
                </div>
            `;
        }

        // 渲染视频分类
        function renderVideoCategory(categoryName, videos) {
            const container = document.getElementById(`${categoryName}-videos`);
            const state = categoryStates[categoryName];

            if (!container || !videos) return;

            const videosToShow = state.expanded ? videos : videos.slice(0, state.showCount);
            container.innerHTML = videosToShow.map(createVideoCard).join('');
        }

        // 切换分类显示状态
        function toggleCategory(categoryName) {
            const state = categoryStates[categoryName];
            const button = document.querySelector(`button[onclick="toggleCategory('${categoryName}')"]`);

            state.expanded = !state.expanded;
            button.textContent = state.expanded ? '收起' : '显示更多';

            // 重新渲染该分类
            if (window.AI_KNOWLEDGE_BASE && window.AI_KNOWLEDGE_BASE.videoDatabase) {
                const videos = window.AI_KNOWLEDGE_BASE.videoDatabase[categoryName];
                renderVideoCategory(categoryName, videos);
            }
        }

        // 初始化视频页面
        function initVideoPage() {
            // 等待配置加载
            if (!window.AI_KNOWLEDGE_BASE || !window.AI_KNOWLEDGE_BASE.videoDatabase) {
                setTimeout(initVideoPage, 100);
                return;
            }

            // 渲染所有视频分类
            const videoDatabase = window.AI_KNOWLEDGE_BASE.videoDatabase;
            Object.keys(videoDatabase).forEach(categoryName => {
                renderVideoCategory(categoryName, videoDatabase[categoryName]);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initVideoPage);

        // 如果配置已经加载，立即初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initVideoPage);
        } else {
            initVideoPage();
        }
    </script>

    <!-- AI智能助手 -->
    <script src="../components/load-assistant.js"></script>
</body>
</html>
