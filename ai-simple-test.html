<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手直接测试</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        /* 直接嵌入智能助手样式 */
        .ai-assistant {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: 'Noto Sans SC', sans-serif;
        }

        .ai-toggle-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            border: none;
        }

        .ai-toggle-btn i {
            color: white;
            font-size: 24px;
        }

        .ai-toggle-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25);
        }

        .ai-pulse {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: rgba(118, 75, 162, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            70% {
                transform: scale(1.3);
                opacity: 0;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        .ai-chat-window {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            z-index: 9998;
        }

        .ai-chat-window.active {
            display: flex;
        }

        .ai-chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ai-header-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ai-chat-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .ai-message {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }

        .ai-message-bot .ai-message-content {
            background: #f1f3f4;
            border-radius: 18px 18px 18px 4px;
            padding: 12px 16px;
            max-width: 80%;
        }

        .ai-message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <h1>🤖 智能助手直接测试</h1>
        <p>这个页面直接嵌入了智能助手代码，用于测试显示效果</p>
        
        <div id="statusBox" class="status-success">
            ✅ 智能助手已直接嵌入，右下角应该有蓝色机器人按钮
        </div>
    </div>

    <!-- 直接嵌入智能助手HTML -->
    <div id="aiAssistant" class="ai-assistant">
        <!-- 悬浮按钮 -->
        <button id="aiAssistantToggle" class="ai-toggle-btn">
            <i class="fas fa-robot"></i>
            <span class="ai-pulse"></span>
        </button>
        
        <!-- 聊天窗口 -->
        <div id="aiChatWindow" class="ai-chat-window">
            <!-- 聊天头部 -->
            <div class="ai-chat-header">
                <div class="ai-header-info">
                    <i class="fas fa-robot"></i>
                    <span>康养助手</span>
                </div>
                <div class="ai-header-actions">
                    <button id="aiCloseBtn" style="background: none; border: none; color: white; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <!-- 聊天内容区域 -->
            <div class="ai-chat-content">
                <div id="aiChatMessages" class="ai-chat-messages">
                    <!-- 欢迎消息 -->
                    <div class="ai-message ai-message-bot">
                        <div class="ai-message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-message-content">
                            <div class="ai-message-text">
                                您好！我是康养平台智能助手 🤖<br><br>
                                我可以帮您：<br>
                                • 🧭 快速找到康复功能<br>
                                • 📚 了解训练方法<br>
                                • 🎯 制定康复计划<br>
                                • ❓ 解答相关问题<br><br>
                                请问有什么可以帮助您的吗？
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的智能助手功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化智能助手...');
            
            const toggleBtn = document.getElementById('aiAssistantToggle');
            const chatWindow = document.getElementById('aiChatWindow');
            const closeBtn = document.getElementById('aiCloseBtn');
            
            if (toggleBtn && chatWindow) {
                console.log('✅ 智能助手元素找到，绑定事件...');
                
                // 点击按钮打开/关闭聊天窗口
                toggleBtn.addEventListener('click', function() {
                    console.log('点击了智能助手按钮');
                    chatWindow.classList.toggle('active');
                });
                
                // 关闭按钮
                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        console.log('点击了关闭按钮');
                        chatWindow.classList.remove('active');
                    });
                }
                
                console.log('✅ 智能助手初始化完成');
            } else {
                console.error('❌ 智能助手元素未找到');
            }
        });
    </script>
</body>
</html>
