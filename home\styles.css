/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #e6f2ff 0%, #f8fbff 100%);
    color: #2c3e50;
    line-height: 1.6;
    min-height: 100vh;
    font-size: 18px;
    margin: 0;
    padding: 0;
}

.container {
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 1400px;
}

/* 主标题样式 */
.main-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 20px;
    background: linear-gradient(135deg, #6ba3d6, #a8c8ec);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(107, 163, 214, 0.2);
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.main-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}



/* 主标题样式 */
.main-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    text-align: center;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    letter-spacing: 2px;
}

/* 副标题样式 */
.main-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    margin: 12px 0 0 0;
    font-weight: 400;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 模块行布局 */
.module-row {
    display: flex;
    gap: 40px;
    height: 200px;
}

/* 模块块样式 */
.module-block {
    flex: 1;
    background: white;
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    padding: 28px 24px;
    min-height: 140px;
    border: 1px solid rgba(0,0,0,0.05);
}

.module-block:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.module-block:active {
    transform: translateY(-2px);
}

.module-block.full-width {
    width: 100%;
}

/* 模块图标样式 */
.module-icon {
    font-size: 44px;
    margin-bottom: 14px;
    opacity: 0.8;
}

.module-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
    text-align: center;
}

/* 模块描述样式 */
.module-description {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    line-height: 1.4;
    font-weight: 400;
    text-align: center;
}



/* 响应式设计 */
@media (max-width: 768px) {
    .module-row {
        flex-direction: column;
        height: auto;
    }

    .module-block {
        min-height: 120px;
        padding: 24px 16px;
    }

    .module-icon {
        font-size: 36px;
        margin-bottom: 12px;
    }

    .main-title {
        font-size: 2rem;
    }

    .module-title {
        font-size: 1.3rem;
    }

    .module-description {
        font-size: 0.8rem;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 1.8rem;
    }

    .module-title {
        font-size: 1.3rem;
    }

    .module-block {
        height: 100px;
    }
}

/* 底部样式 */
.footer {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    font-size: 16px;
    border-top: 1px solid #ddd;
    margin-top: 40px;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 242, 245, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
    color: #4a90e2;
}

.loading-spinner i {
    font-size: 48px;
    animation: pulse 1.5s ease-in-out infinite;
    margin-bottom: 16px;
    display: block;
}

.loading-spinner p {
    font-size: 20px;
    font-weight: 500;
}

/* 动画定义 */
@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
