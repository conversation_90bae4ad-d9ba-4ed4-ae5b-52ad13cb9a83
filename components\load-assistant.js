/**
 * 智能助手加载脚本
 * 用于在所有页面动态加载智能助手组件
 */

(function() {
    // 检查是否已加载
    if (window.aiAssistantLoaded || document.querySelector('#aiAssistant')) {
        console.log('AI助手已加载，跳过重复加载');
        return;
    }

    // 标记正在加载
    window.aiAssistantLoaded = true;
    
    // 创建样式链接
    function loadStyles() {
        return new Promise((resolve) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/components/ai-assistant.css';
            link.onload = resolve;
            document.head.appendChild(link);
        });
    }
    
    // 加载Font Awesome (如果尚未加载)
    function loadFontAwesome() {
        return new Promise((resolve) => {
            if (document.querySelector('link[href*="font-awesome"]')) {
                resolve();
                return;
            }
            
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
            link.onload = resolve;
            document.head.appendChild(link);
        });
    }
    
    // 加载HTML组件
    function loadHTML() {
        return fetch('/components/ai-assistant.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                const div = document.createElement('div');
                div.innerHTML = html.trim();
                const aiAssistant = div.firstElementChild;
                if (aiAssistant) {
                    document.body.appendChild(aiAssistant);
                    console.log('AI助手HTML组件已加载');
                } else {
                    throw new Error('AI助手HTML组件为空');
                }
            });
    }
    
    // 加载配置文件
    function loadConfig() {
        return new Promise((resolve, reject) => {
            if (window.DIFY_CONFIG) {
                console.log('配置文件已存在，跳过加载');
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/components/ai-config.js';
            script.onload = () => {
                console.log('配置文件加载成功');
                resolve();
            };
            script.onerror = () => reject(new Error('配置文件加载失败'));
            document.head.appendChild(script);
        });
    }

    // 加载主脚本
    function loadScript() {
        return new Promise((resolve, reject) => {
            if (window.AIAssistant) {
                console.log('主脚本已存在，跳过加载');
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/components/ai-assistant.js';
            script.onload = () => {
                console.log('主脚本加载成功');
                resolve();
            };
            script.onerror = () => reject(new Error('主脚本加载失败'));
            document.head.appendChild(script);
        });
    }
    
    // 按顺序加载所有资源
    console.log('开始加载AI助手...');

    Promise.all([loadStyles(), loadFontAwesome()])
        .then(() => {
            console.log('样式文件加载完成');
            return loadHTML();
        })
        .then(() => {
            console.log('HTML组件加载完成');
            // 确保HTML已插入DOM
            return new Promise(resolve => setTimeout(resolve, 100));
        })
        .then(() => {
            return loadConfig();
        })
        .then(() => {
            console.log('配置文件加载完成');
            // 确保配置已加载
            return new Promise(resolve => setTimeout(resolve, 100));
        })
        .then(() => {
            return loadScript();
        })
        .then(() => {
            console.log('主脚本加载完成');
            // 给更多时间让脚本初始化
            return new Promise(resolve => setTimeout(resolve, 500));
        })
        .then(() => {
            console.log('AI助手加载完成');
            // 验证加载结果
            const button = document.querySelector('#aiAssistantToggle');
            const assistant = document.querySelector('#aiAssistant');

            if (button && assistant) {
                console.log('✓ AI助手加载成功');
                // 确保按钮可见
                assistant.style.display = 'block';
                button.style.display = 'flex';
            } else {
                console.error('✗ AI助手元素未找到:', {
                    button: !!button,
                    assistant: !!assistant
                });
            }
        })
        .catch(error => {
            console.error('加载AI助手失败:', error);
            window.aiAssistantLoaded = false; // 重置加载状态
        });
})();
