# 康养平台智能体工作流搭建指南

## 🎯 总体目标
搭建一个能够理解用户问题、生成精准链接、指导操作流程的智能体系统。

## 📋 准备工作清单

### ✅ 环境检查
- [ ] Node.js 环境 (v16+)
- [ ] Vite 开发服务器
- [ ] 现代浏览器 (Chrome/Firefox/Safari)
- [ ] Dify 账号和 API Key

### ✅ 文件确认
- [ ] `/components/` 目录完整
- [ ] `dify-setup-guide.md` 存在
- [ ] `package.json` 配置正确

## 🚀 第一阶段：环境搭建与基础测试

### 步骤 1.1：启动开发环境
```bash
# 在项目根目录执行
npm run dev
```

**测试验证**：
- 访问 `http://localhost:3002`
- 确认页面正常加载
- 检查控制台无错误

### 步骤 1.2：测试现有智能助手
```bash
# 访问测试页面
http://localhost:3002/ai-assistant-demo.html
```

**测试验证**：
- [ ] 智能助手悬浮按钮显示
- [ ] 点击按钮打开聊天窗口
- [ ] 界面样式正常

### 步骤 1.3：检查 Dify 配置
打开 `components/ai-config.js` 检查配置：

```javascript
const DIFY_CONFIG = {
    apiKey: 'YOUR_DIFY_API_KEY_HERE',
    baseUrl: 'https://api.dify.ai/v1',
    appId: 'YOUR_DIFY_APP_ID_HERE'
};
```

**测试验证**：
- [ ] API Key 已配置
- [ ] Base URL 正确
- [ ] App ID 已设置

## 🧠 第二阶段：Dify 智能体配置

### 步骤 2.1：创建 Dify 应用
1. 登录 [Dify 控制台](https://dify.ai)
2. 点击"创建应用" → "聊天助手"
3. 应用名称：`康养平台智能助手`

**测试验证**：
- [ ] 应用创建成功
- [ ] 获得应用 ID

### 步骤 2.2：配置系统提示词
复制以下提示词到 Dify 系统设置：

```
你是康养平台的专业智能助手，专门为脑卒中康复患者提供服务。

## 核心功能模块：
🏠 主页: /home/<USER>
📊 训练康复: /training/index.html
  ├─ 🧠 认知训练: /training/cognitive_function_training/index.html
  ├─ 🗣️ 语言训练: /training/language_function_training/index.html  
  ├─ 👁️ 视觉训练: /training/visual_impairment_training/index.html
  └─ 😊 情绪训练: /training/emotional_regulation_training/index.html
📈 量表评估: /assessment/index.html
💬 老人论坛: /forum/index.html
📹 短视频: /videos/index.html
🎯 积分系统: /points/index.html

## 回复格式：
🎯 **[功能名称](链接)**
功能说明

📋 **具体训练：**
- [子功能](链接) - 说明

💡 **使用建议：**
具体指导

请用温暖、专业的语气回答，并提供可点击的功能链接。
```

**测试验证**：
- [ ] 提示词保存成功
- [ ] 测试对话响应正常

### 步骤 2.3：配置开场白
```
您好！我是康养平台的智能助手 🤖

我可以帮您：
• 🧭 快速找到需要的康复功能
• 📚 了解各种训练方法
• 🎯 制定个性化康复计划
• ❓ 解答康复相关问题

请告诉我您的康复需求或想了解的功能？
```

**测试验证**：
- [ ] 开场白显示正确
- [ ] 语气温暖专业

## 🔗 第三阶段：智能链接生成测试

### 步骤 3.1：基础功能测试
在 Dify 测试以下对话：

**测试用例 1：记忆问题**
- 输入：`我记忆力很差，经常忘事`
- 期望：推荐记忆力训练链接

**测试用例 2：语言困难**  
- 输入：`说话有困难，表达不清楚`
- 期望：推荐语言功能训练链接

**测试用例 3：情绪问题**
- 输入：`心情不好，很抑郁`
- 期望：推荐情绪调节训练链接

**测试验证**：
- [ ] 能识别用户需求
- [ ] 提供正确功能链接
- [ ] 回复格式规范

### 步骤 3.2：获取 API 密钥
1. 在 Dify 应用设置中找到"API 访问"
2. 复制 API 密钥
3. 记录应用 ID

**测试验证**：
- [ ] API 密钥获取成功
- [ ] 应用 ID 记录正确

## 🔧 第四阶段：平台集成配置

### 步骤 4.1：更新配置文件
编辑 `components/ai-config.js`：

```javascript
const DIFY_CONFIG = {
    apiKey: 'app-你的API密钥',
    baseUrl: 'https://api.dify.ai/v1',
    appId: '你的应用ID'
};
```

**测试验证**：
- [ ] 配置文件更新成功
- [ ] 语法无错误

### 步骤 4.2：测试 API 连接
在浏览器控制台执行：

```javascript
// 测试 API 连接
fetch('https://api.dify.ai/v1/chat-messages', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        inputs: {},
        query: '测试连接',
        user: 'test-user'
    })
}).then(response => response.json())
  .then(data => console.log('API 测试结果:', data));
```

**测试验证**：
- [ ] API 调用成功
- [ ] 返回正常响应

### 步骤 4.3：集成测试
1. 重启开发服务器：`npm run dev`
2. 访问：`http://localhost:3002/home/<USER>
3. 点击智能助手按钮
4. 测试对话功能

**测试验证**：
- [ ] 智能助手正常显示
- [ ] 对话功能正常
- [ ] 链接可以点击跳转

## 📊 第五阶段：功能验证与优化

### 步骤 5.1：完整功能测试
测试所有主要功能模块的链接跳转：

| 功能模块 | 测试输入 | 期望链接 | 结果 |
|---------|---------|---------|------|
| 认知训练 | "记忆力差" | /training/cognitive_function_training/ | ✅ |
| 语言训练 | "说话困难" | /training/language_function_training/ | ✅ |
| 视觉训练 | "看不清楚" | /training/visual_impairment_training/ | ✅ |
| 情绪训练 | "心情不好" | /training/emotional_regulation_training/ | ✅ |
| 量表评估 | "想评估进度" | /assessment/ | ✅ |
| 老人论坛 | "想交流" | /forum/ | ✅ |
| 短视频 | "学习知识" | /videos/ | ✅ |
| 积分系统 | "获得奖励" | /points/ | ✅ |

### 步骤 5.2：用户体验测试
- [ ] 响应速度 < 3秒
- [ ] 界面友好易用
- [ ] 链接跳转正确
- [ ] 移动端适配良好

### 步骤 5.3：错误处理测试
- [ ] 网络断开时的处理
- [ ] API 调用失败的处理
- [ ] 无效输入的处理

## 🎉 完成检查清单

### 基础功能
- [ ] 智能助手正常显示和隐藏
- [ ] 对话界面美观易用
- [ ] Dify API 连接正常

### 智能功能  
- [ ] 能理解用户康复需求
- [ ] 自动生成正确功能链接
- [ ] 提供专业康复建议

### 导航功能
- [ ] 所有功能模块链接正确
- [ ] 页面跳转正常
- [ ] 移动端兼容性良好

### 用户体验
- [ ] 响应速度快
- [ ] 界面友好
- [ ] 错误处理完善

## 🔄 持续优化建议

1. **收集用户反馈**：定期收集用户使用反馈
2. **优化提示词**：根据实际使用情况调整
3. **扩展知识库**：添加更多康复相关知识
4. **性能监控**：监控 API 调用性能和成功率

---

🎊 **恭喜！您已成功搭建康养平台智能体工作流！**

现在用户可以通过自然语言与智能助手对话，获得精准的功能导航和专业的康复指导。
