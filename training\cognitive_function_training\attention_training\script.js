// 全局变量
let currentQuestionIndex = 0;
let totalScore = 0;
let questions = [];
let currentQuestion = null;
let audioPlayer = null;
let isPlaying = false;
let clickCount = 0;
let correctClicks = 0;
let questionResults = [];
let audioStartTime = 0;
let targetWordTimings = [];
let currentWordIndex = 0;
let lastClickTime = 0;
let progressInterval = null;
let actualAudioDuration = 0;

// 题库文件列表
const questionBankFiles = [
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/daily_items_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/home_furniture.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/food_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/nature_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/transportation_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/stationery_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/clothing_extended.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/sports.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/learning.json',
    '../../../question_bank/cognitive_function_training_bank/attention_training_bank/listen_and_press/tools.json'
];

// 备用题目数据（防止文件加载失败）
const fallbackQuestions = [
    {
        id: 1,
        target_word: "毛巾",
        text: "卫生间的毛巾挂在挂钩上，蓝色毛巾用来擦脸，粉色毛巾用来擦手，洗完澡用毛巾擦干身体，毛巾要经常晒太阳。",
        target_count: 5
    },
    {
        id: 2,
        target_word: "苹果",
        text: "红色的苹果很甜，绿色苹果有点酸，妈妈买了一袋苹果，我最喜欢吃苹果了，苹果含有丰富的维生素。",
        target_count: 5
    },
    {
        id: 3,
        target_word: "公交车",
        text: "早上坐公交车上班，公交车很拥挤，下午的公交车人少一些，公交车是绿色环保的交通工具，我每天都坐公交车。",
        target_count: 5
    }
];

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    audioPlayer = document.getElementById('audio-player');
    setupAudioEvents();
});

// 设置音频事件
function setupAudioEvents() {
    audioPlayer.addEventListener('timeupdate', updateProgress);
    audioPlayer.addEventListener('ended', onAudioEnded);
    audioPlayer.addEventListener('loadstart', function() {
        document.getElementById('progress-bar').style.width = '0%';
    });
}

// 开始训练
async function startTraining() {
    try {
        // 显示加载状态
        const startBtn = document.querySelector('.start-btn');
        if (startBtn) {
            startBtn.textContent = '加载中...';
            startBtn.disabled = true;
        }

        // 加载题目
        await loadRandomQuestions();

        // 切换到训练页面
        showPage('training-page');

        // 开始第一题
        startQuestion();

    } catch (error) {
        console.error('启动训练失败:', error);
        alert(`启动训练失败: ${error.message}\n请刷新页面重试`);
        const startBtn = document.querySelector('.start-btn');
        if (startBtn) {
            startBtn.textContent = '开始训练';
            startBtn.disabled = false;
        }
    }
}

// 加载随机题目
async function loadRandomQuestions() {
    const allQuestions = [];

    // 加载所有题库文件
    for (const file of questionBankFiles) {
        try {
            const response = await fetch(file);
            if (response.ok) {
                const data = await response.json();
                if (data && data.exercises && Array.isArray(data.exercises)) {
                    allQuestions.push(...data.exercises);
                }
            }
        } catch (error) {
            // 静默处理加载错误
        }
    }

    // 如果没有加载到题目，使用备用题目
    if (allQuestions.length === 0) {
        allQuestions.push(...fallbackQuestions);
    }

    // 随机选择3道题
    questions = getRandomQuestions(allQuestions, 3);
}

// 获取随机题目
function getRandomQuestions(allQuestions, count) {
    const shuffled = [...allQuestions].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
}

// 开始当前题目
function startQuestion() {
    currentQuestion = questions[currentQuestionIndex];
    clickCount = 0;
    correctClicks = 0;
    currentWordIndex = 0;
    lastClickTime = 0;

    // 分析目标词在文本中的位置和时机
    analyzeTargetWordTimings();

    // 更新UI
    updateQuestionUI();

    // 重置状态
    resetQuestionState();

    // 生成语音
    generateAudio(currentQuestion.text);
}

// 更新题目UI
function updateQuestionUI() {
    document.getElementById('current-num').textContent = currentQuestionIndex + 1;
    document.getElementById('target-word').textContent = currentQuestion.target_word;
    document.getElementById('expected-count').textContent = currentQuestion.target_count;
    document.getElementById('actual-count').textContent = clickCount;
    document.getElementById('question-score').textContent = 0;
}

// 分析目标词在文本中的时机和有效点击窗口
function analyzeTargetWordTimings() {
    const text = currentQuestion.text;
    const targetWord = currentQuestion.target_word;
    targetWordTimings = [];

    let index = 0;
    while ((index = text.indexOf(targetWord, index)) !== -1) {
        // 计算目标词的开始和结束位置
        const wordStart = index;
        const wordEnd = index + targetWord.length - 1;

        // 计算有效点击窗口：从目标词的第一个字开始，到词汇结束后再读两个字
        const windowStart = wordStart; // 从目标词的第一个字开始

        // 精确计算：词汇结束后再读两个字符
        // 例如："本子，这个" -> 词汇"本子"结束后，再读"，"和"这"两个字符，窗口到"这"结束
        let windowEnd = wordEnd; // 从词汇结束位置开始
        let pos = wordEnd + 1; // 从词汇后的第一个字符开始
        let additionalChars = 0;

        // 向后读取两个字符（不管是汉字、标点还是其他字符）
        while (pos < text.length && additionalChars < 2) {
            windowEnd = pos; // 更新窗口结束位置
            additionalChars++;
            pos++;
        }

        // 确保不超过文本长度
        windowEnd = Math.min(windowEnd, text.length - 1);

        // 基于字符数量计算更精确的时间位置
        // 考虑不同字符的播放时长
        const windowStartTime = calculateCharacterTime(text, windowStart);
        const windowEndTime = calculateCharacterTime(text, windowEnd + 1); // +1 因为我们要包含windowEnd这个字符

        targetWordTimings.push({
            position: index,
            wordStart: wordStart,
            wordEnd: wordEnd,
            windowStart: windowStart,
            windowEnd: windowEnd,
            windowStartTime: windowStartTime,
            windowEndTime: windowEndTime,
            found: false
        });
        index += targetWord.length;
    }


}

// 计算字符在文本中的相对时间位置
function calculateCharacterTime(text, charIndex) {
    if (charIndex >= text.length) {
        return 1.0;
    }

    // 计算到指定位置的累积播放时间
    let totalTime = 0;
    for (let i = 0; i < charIndex; i++) {
        const char = text[i];
        if (/[\u4e00-\u9fa5]/.test(char)) {
            // 中文字符，考虑0.8语速
            totalTime += 250; // 250ms per character at 0.8 speed
        } else if (/[，。！？；：、]/.test(char)) {
            // 标点符号，有停顿
            totalTime += 400;
        } else if (/\s/.test(char)) {
            // 空格
            totalTime += 50;
        } else {
            // 其他字符
            totalTime += 180;
        }
    }

    // 计算总文本的播放时间
    const totalDuration = estimateTextDuration(text);

    // 返回相对时间位置（0-1之间）
    return Math.min(totalTime / totalDuration, 1.0);
}

// 重置题目状态（只播放一次版本）
function resetQuestionState() {
    // 清理之前的进度条interval
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }

    // 显示播放按钮，但会在播放后隐藏
    document.getElementById('play-btn').style.display = 'inline-block';
    document.getElementById('play-btn').disabled = false;
    document.getElementById('play-btn').innerHTML = '<span class="play-icon">▶</span>播放';

    // 重置点击按钮状态 - 确保新题目开始时可以点击
    document.getElementById('click-btn').disabled = true; // 播放开始前禁用
    document.getElementById('next-btn').style.display = 'none';
    document.getElementById('click-feedback').textContent = '准备播放...';
    document.getElementById('click-feedback').className = 'click-feedback';
    document.getElementById('progress-bar').style.width = '0%';
    audioStartTime = 0;

    // 自动开始播放（只播放一次）
    setTimeout(() => {
        playAudio();
    }, 1500); // 1.5秒后自动开始播放
}

// 生成语音
function generateAudio(text) {
    // 使用Web Speech API生成语音
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'zh-CN';
        utterance.rate = 0.8; // 稍慢的语速
        utterance.pitch = 1;
        utterance.volume = 1;
        
        currentQuestion.utterance = utterance;
    } else {
        alert('您的浏览器不支持语音功能，请使用现代浏览器');
    }
}

// 播放音频（只能播放一次）
function playAudio() {
    if (!currentQuestion.utterance) {
        alert('音频未准备好，请稍后再试');
        return;
    }

    if (isPlaying) {
        return;
    }

    isPlaying = true;
    // 隐藏播放按钮，因为只能播放一次
    document.getElementById('play-btn').style.display = 'none';
    document.getElementById('click-btn').disabled = false;

    // 显示播放状态
    document.getElementById('click-feedback').textContent = '播放中...';
    document.getElementById('click-feedback').className = 'click-feedback playing';

    // 设置语音事件
    currentQuestion.utterance.onstart = function() {
        audioStartTime = Date.now(); // 记录开始时间
        actualAudioDuration = 0; // 重置实际时长
        startRealTimeProgress();
    };

    currentQuestion.utterance.onend = function() {
        actualAudioDuration = Date.now() - audioStartTime; // 记录实际播放时长

        // 给进度条一点时间平滑过渡到100%
        setTimeout(() => {
            onAudioEnded();
        }, 200); // 200ms延迟，让进度条平滑完成
    };

    currentQuestion.utterance.onerror = function(event) {
        onAudioEnded();
    };

    // 播放语音
    speechSynthesis.speak(currentQuestion.utterance);
}

// 实时进度条（基于实际播放时间）- 优化版本
function startRealTimeProgress() {
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = '0%';

    // 清除之前的进度条interval
    if (progressInterval) {
        clearInterval(progressInterval);
    }

    const updateInterval = 30; // 更频繁的更新间隔（毫秒）
    let lastProgress = 0;

    progressInterval = setInterval(() => {
        if (!isPlaying || !audioStartTime) {
            clearInterval(progressInterval);
            return;
        }

        const currentTime = Date.now();
        const elapsedTime = currentTime - audioStartTime;

        // 使用动态估算的播放时长
        const estimatedDuration = estimateTextDuration(currentQuestion.text);

        // 计算基础进度百分比
        let progress = (elapsedTime / estimatedDuration) * 100;

        // 检查语音是否还在播放
        const isSpeaking = speechSynthesis.speaking;

        if (isSpeaking) {
            // 如果还在播放，限制最大进度为92%，避免过早到达终点
            progress = Math.min(progress, 92);
        } else if (elapsedTime > 500) {
            // 如果语音已停止且已播放超过0.5秒，平滑过渡到100%
            const targetProgress = 100;
            const progressDiff = targetProgress - lastProgress;

            if (progressDiff > 0) {
                // 平滑过渡，每次增加剩余进度的20%
                progress = lastProgress + (progressDiff * 0.2);

                // 如果非常接近100%，直接设为100%
                if (progress >= 99.5) {
                    progress = 100;
                    clearInterval(progressInterval);
                }
            } else {
                progress = 100;
                clearInterval(progressInterval);
            }
        }

        // 确保进度不会倒退
        progress = Math.max(progress, lastProgress);
        lastProgress = progress;

        progressBar.style.width = progress + '%';
    }, updateInterval);
}

// 估算文本播放时长（毫秒）- 优化版本
function estimateTextDuration(text) {
    // 更准确的中文语音时长估算，考虑语速0.8
    const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const punctuationCount = (text.match(/[，。！？；：、]/g) || []).length;
    const spaceCount = (text.match(/\s/g) || []).length;
    const otherCharCount = text.length - chineseCharCount - punctuationCount - spaceCount;

    // 基于语速0.8调整时长：
    // 中文字符：每个字约250ms（考虑0.8语速），标点符号：每个约400ms（停顿），其他字符：每个约180ms
    const baseRate = 1.25; // 0.8语速的倒数
    const estimatedDuration = (chineseCharCount * 200 * baseRate) +
                             (punctuationCount * 320 * baseRate) +
                             (otherCharCount * 150 * baseRate) +
                             (spaceCount * 50); // 空格的短暂停顿

    // 添加开始和结束的缓冲时间
    return Math.max(estimatedDuration, 1500) + 800; // 最少1.5秒，额外0.8秒缓冲
}



// 音频播放结束（只能播放一次版本）
function onAudioEnded() {
    isPlaying = false;

    // 清理进度条interval
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }

    // 确保进度条显示100%
    document.getElementById('progress-bar').style.width = '100%';

    // 不恢复播放按钮，因为每题只能播放一次
    document.getElementById('click-btn').disabled = true;

    // 清空反馈信息
    document.getElementById('click-feedback').textContent = '';
    document.getElementById('click-feedback').className = 'click-feedback';

    // 计算得分
    calculateScore();

    // 显示下一题按钮
    document.getElementById('next-btn').style.display = 'inline-block';
}

// 处理点击 - 优化版本
function handleClick() {
    if (!isPlaying) {
        return;
    }

    // 计算最大允许点击次数（目标次数 + 2）
    const maxAllowedClicks = currentQuestion.target_count + 2;

    // 检查是否已达到最大点击次数
    if (clickCount >= maxAllowedClicks) {
        showClickFeedback(`已达到最大点击次数 (${maxAllowedClicks})`, 'limit-reached');
        return;
    }

    // 立即更新点击计数
    clickCount++;
    document.getElementById('actual-count').textContent = clickCount;

    // 立即显示临时反馈，避免用户感觉卡顿
    const feedback = document.getElementById('click-feedback');
    feedback.textContent = '检查中...';
    feedback.className = 'click-feedback checking';

    // 使用setTimeout确保UI立即更新，然后进行时机检查
    setTimeout(() => {
        const isCorrect = checkClickTiming();

        if (isCorrect) {
            correctClicks++;
            // 根据点击情况给出不同的反馈
            if (clickCount <= currentQuestion.target_count) {
                showClickFeedback(`很好！第${clickCount}次`, 'correct');
            } else if (clickCount <= maxAllowedClicks) {
                showClickFeedback(`听到了！第${clickCount}次`, 'correct');
            }
        } else {
            // 根据具体情况给出不同的错误反馈
            if (clickCount > currentQuestion.target_count) {
                const remaining = maxAllowedClicks - clickCount;
                showClickFeedback(`第${clickCount}次 - 再仔细听 (还可点击${remaining}次)`, 'incorrect');
            } else {
                showClickFeedback(`第${clickCount}次 - 再仔细听`, 'incorrect');
            }
        }

        // 如果达到最大点击次数，禁用按钮
        if (clickCount >= maxAllowedClicks) {
            document.getElementById('click-btn').disabled = true;
            showClickFeedback(`点击次数已用完，请等待播放结束`, 'limit-reached');
        }
    }, 0); // 使用0延迟确保UI更新
}

// 检查点击时机是否正确（基于精确时间窗口）- 优化版本
function checkClickTiming() {
    if (!audioStartTime) {
        return false; // 音频还没开始播放
    }

    const currentTime = Date.now();
    const elapsedTime = currentTime - audioStartTime;
    const estimatedDuration = estimateTextDuration(currentQuestion.text);
    const currentProgress = Math.min(elapsedTime / estimatedDuration, 1);

    // 检查点击间隔，避免连续快速点击
    const timeSinceLastClick = lastClickTime ? currentTime - lastClickTime : 1000;
    lastClickTime = currentTime;

    // 连续快速点击（小于300ms）直接判为错误
    if (timeSinceLastClick < 300) {
        return false;
    }

    // 检查是否在任何有效窗口内
    let foundValidWindow = false;

    for (let i = 0; i < targetWordTimings.length; i++) {
        const timing = targetWordTimings[i];

        // 检查是否在有效窗口内且未被识别过
        if (currentProgress >= timing.windowStartTime &&
            currentProgress <= timing.windowEndTime &&
            !timing.found) {

            // 只有在有效窗口内点击才标记为已找到
            timing.found = true;
            foundValidWindow = true;
            console.log(`✅ 点击正确！匹配第${i+1}个目标词窗口 (${Math.round(currentProgress * 100)}%)`);
            break; // 找到一个匹配的窗口就退出
        }
    }

    if (!foundValidWindow) {
        console.log(`❌ 点击错误！当前进度${Math.round(currentProgress * 100)}%不在任何有效窗口内`);
        // 显示当前所有窗口状态
        targetWordTimings.forEach((timing, i) => {
            const status = timing.found ? '已找到' : '未找到';
            console.log(`  窗口${i+1}: ${Math.round(timing.windowStartTime * 100)}%-${Math.round(timing.windowEndTime * 100)}% (${status})`);
        });
    }

    return foundValidWindow;
}

// 获取当前音频播放进度（0-1）
function getCurrentAudioProgress() {
    const progressBar = document.getElementById('progress-bar');
    const width = progressBar.style.width;
    return parseFloat(width) / 100 || 0;
}

// 查找目标词在文本中的位置
function findTargetWordPositions(text, targetWord) {
    const positions = [];
    let index = 0;

    while ((index = text.indexOf(targetWord, index)) !== -1) {
        positions.push(index);
        index += targetWord.length;
    }

    return positions;
}

// 显示点击反馈 - 优化版本
function showClickFeedback(message, type) {
    const feedback = document.getElementById('click-feedback');

    // 立即清除之前的定时器
    if (feedback.clearTimer) {
        clearTimeout(feedback.clearTimer);
    }

    // 立即更新内容和样式
    feedback.textContent = message;
    feedback.className = `click-feedback ${type}`;

    // 强制重绘以确保立即显示
    feedback.offsetHeight;

    // 1.5秒后清除反馈
    feedback.clearTimer = setTimeout(() => {
        feedback.textContent = '';
        feedback.className = 'click-feedback';
        feedback.clearTimer = null;
    }, 1500);
}

// 计算得分
function calculateScore() {
    const expectedCount = currentQuestion.target_count;
    const actualCount = clickCount;

    // 更加宽容的计分逻辑（满分100分）
    let score = 0;

    if (actualCount === 0) {
        score = 0; // 完全没点击
    } else {
        // 基础参与分：只要点击就有基础分
        let baseScore = 30;

        // 正确点击奖励
        const correctRatio = Math.min(correctClicks / expectedCount, 1);
        const correctBonus = correctRatio * 50; // 最多50分

        // 数量准确度奖励
        let accuracyBonus = 0;
        const deviation = Math.abs(actualCount - expectedCount);
        if (deviation === 0) {
            accuracyBonus = 20; // 完全准确
        } else if (deviation <= 1) {
            accuracyBonus = 15; // 偏差1次
        } else if (deviation <= 2) {
            accuracyBonus = 10; // 偏差2次
        } else if (deviation <= 3) {
            accuracyBonus = 5; // 偏差3次
        }

        // 轻微的过度点击惩罚
        let penalty = 0;
        if (actualCount > expectedCount * 2.5) {
            penalty = 15; // 严重过度点击
        } else if (actualCount > expectedCount * 2) {
            penalty = 10; // 中度过度点击
        } else if (actualCount > expectedCount * 1.5) {
            penalty = 5; // 轻度过度点击
        }

        score = Math.max(20, Math.round(baseScore + correctBonus + accuracyBonus - penalty));
    }

    // 更新显示
    document.getElementById('question-score').textContent = score;
    totalScore += score;
    document.getElementById('total-score').textContent = totalScore;

    // 保存结果
    questionResults.push({
        questionIndex: currentQuestionIndex + 1,
        targetWord: currentQuestion.target_word,
        expectedCount: expectedCount,
        actualCount: actualCount,
        correctClicks: Math.round(correctClicks * 10) / 10, // 保留一位小数
        score: score
    });
}

// 下一题
function nextQuestion() {
    currentQuestionIndex++;
    
    if (currentQuestionIndex < questions.length) {
        startQuestion();
    } else {
        showResults();
    }
}

// 显示结果
function showResults() {
    showPage('result-page');
    
    // 显示最终得分
    document.getElementById('final-score').textContent = totalScore;
    
    // 显示详细结果
    const detailsContainer = document.getElementById('result-details');
    let detailsHTML = '<h3>详细结果</h3>';
    
    questionResults.forEach(result => {
        detailsHTML += `
            <div style="margin-bottom: 20px; padding: 15px; background: white; border-radius: 10px;">
                <h4>第${result.questionIndex}题 - 目标词: ${result.targetWord}</h4>
                <p>应点击次数: ${result.expectedCount} | 实际点击: ${result.actualCount} | 得分: ${result.score}</p>
            </div>
        `;
    });
    
    detailsContainer.innerHTML = detailsHTML;
}

// 重新开始训练
function restartTraining() {
    // 重置所有变量
    currentQuestionIndex = 0;
    totalScore = 0;
    questions = [];
    currentQuestion = null;
    clickCount = 0;
    correctClicks = 0;
    questionResults = [];
    
    // 停止语音
    if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
    }
    
    // 重置UI
    document.getElementById('total-score').textContent = '0';
    
    // 回到介绍页面
    showPage('intro-page');
    
    // 重置开始按钮
    document.querySelector('.start-btn').textContent = '开始训练';
    document.querySelector('.start-btn').disabled = false;
}

// 返回上级
function goBack() {
    // 停止语音
    if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
    }
    
    window.location.href = '../index.html';
}

// 显示指定页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示指定页面
    document.getElementById(pageId).classList.add('active');
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
    }
});
