<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>康养平台 - Dify智能助手集成测试</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .navigation-links {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .nav-link {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        
        .nav-link:hover {
            background: #5a67d8;
        }
        
        /* Dify聊天窗口样式优化 */
        #dify-chatbot-bubble-button {
            background-color: #667eea !important;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
        }
        
        #dify-chatbot-bubble-window {
            width: 380px !important;
            height: 600px !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            #dify-chatbot-bubble-window {
                width: 90vw !important;
                height: 70vh !important;
                max-width: 350px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 康养平台智能助手</h1>
            <p>Dify集成测试页面</p>
        </div>
        
        <div class="test-section">
            <h3>📊 集成状态检查</h3>
            
            <div class="test-item">
                <strong>API配置状态</strong>
                <span class="status success">✅ 已配置</span>
                <div class="code-block">API Key: app-mkGNIAGwOq1qcW2toI6IruXZ</div>
            </div>
            
            <div class="test-item">
                <strong>Dify嵌入代码</strong>
                <span class="status info">🔄 已集成</span>
                <p>智能助手聊天按钮应该出现在页面右下角</p>
            </div>
            
            <div class="test-item">
                <strong>测试说明</strong>
                <p>点击右下角的聊天按钮，尝试以下测试：</p>
                <ul>
                    <li>输入："你好" - 测试基本问候</li>
                    <li>输入："我记忆力很差" - 测试症状识别</li>
                    <li>输入："说话困难" - 测试功能推荐</li>
                    <li>输入："心情不好" - 测试情绪支持</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 期望的智能体回复格式</h3>
            <div class="test-item">
                <strong>标准回复应包含：</strong>
                <div class="code-block">🎯 **[功能名称](功能链接)**
功能简介和适用说明

📋 **推荐训练：**
- [具体训练1](链接) - 针对性说明
- [具体训练2](链接) - 针对性说明

💡 **使用建议：**
- 具体的使用指导
- 训练频次建议
- 注意事项

🌟 康复是循序渐进的过程，请保持信心！</div>
            </div>
        </div>
        
        <div class="navigation-links">
            <a href="/home/<USER>" class="nav-link">🏠 返回主页</a>
            <a href="/test-framework.html" class="nav-link">🧪 运行测试框架</a>
            <a href="/training/index.html" class="nav-link">📊 训练康复</a>
            <a href="/assessment/index.html" class="nav-link">📈 量表评估</a>
        </div>
    </div>

    <!-- Dify智能助手嵌入代码 -->
    <script>
        window.difyChatbotConfig = {
            token: 'vSE7UiS3QeR76ycy',
            baseUrl: 'https://api.dify.ai',  // 修改为官方API地址
            systemVariables: {
                platform: 'kangyang-platform',
                version: '1.0.0'
            },
            userVariables: {
                user_type: 'patient_or_family'
            },
        }
    </script>
    <script
        src="https://udify.app/embed.min.js"
        id="vSE7UiS3QeR76ycy"
        defer>
    </script>

    <!-- 页面加载完成后的提示 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 康养平台智能助手已加载');
            console.log('📝 API Key:', 'app-mkGNIAGwOq1qcW2toI6IruXZ');
            console.log('🔗 Token:', 'vSE7UiS3QeR76ycy');
            
            // 检查Dify脚本是否加载成功
            setTimeout(() => {
                const chatButton = document.querySelector('#dify-chatbot-bubble-button');
                if (chatButton) {
                    console.log('✅ Dify聊天按钮加载成功');
                } else {
                    console.log('❌ Dify聊天按钮未找到，请检查网络连接或token配置');
                }
            }, 3000);
        });
    </script>
</body>
</html>
