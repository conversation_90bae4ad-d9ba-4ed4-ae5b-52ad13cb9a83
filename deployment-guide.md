# 康养平台智能体部署与集成指南

## 🚀 快速部署步骤

### 第一步：环境准备
```bash
# 1. 确保 Node.js 环境
node --version  # 应该 >= 16.0.0

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev
```

### 第二步：Dify 配置
1. **创建 Dify 应用**
   - 访问 [Dify 控制台](https://dify.ai)
   - 创建"聊天助手"类型应用
   - 应用名称：`康养平台智能助手`

2. **配置系统提示词**
   - 复制 `dify-optimized-prompt.md` 中的系统提示词
   - 粘贴到 Dify 应用的系统提示词设置中

3. **设置开场白**
   - 复制优化版开场白到 Dify 应用设置

4. **获取 API 信息**
   - 复制 API Key
   - 记录应用 ID

### 第三步：更新配置文件
编辑 `components/ai-config.js`：
```javascript
const DIFY_CONFIG = {
    apiKey: 'app-你的实际API密钥',
    baseUrl: 'https://api.dify.ai/v1',
    appId: '你的实际应用ID',
    user: 'kangyang-platform-user'
};
```

### 第四步：功能测试
1. **访问测试页面**
   ```
   http://localhost:3002/test-framework.html
   ```

2. **运行自动化测试**
   - 点击"运行所有测试"按钮
   - 查看测试结果和日志

3. **手动功能测试**
   ```
   http://localhost:3002/home/<USER>
   ```
   - 点击智能助手按钮
   - 测试对话功能
   - 验证链接跳转

## 🔧 集成验证清单

### ✅ 基础功能验证
- [ ] 开发服务器正常运行
- [ ] 智能助手悬浮按钮显示
- [ ] 聊天窗口正常打开/关闭
- [ ] 界面样式美观适配

### ✅ API 连接验证
- [ ] Dify API 配置正确
- [ ] API 调用成功响应
- [ ] 错误处理机制正常
- [ ] 网络异常处理完善

### ✅ 智能对话验证
- [ ] 基础问候功能正常
- [ ] 症状识别准确
- [ ] 功能推荐精准
- [ ] 回复格式规范

### ✅ 链接导航验证
- [ ] 所有功能模块链接正确
- [ ] 页面跳转正常
- [ ] 子功能导航准确
- [ ] 移动端兼容性良好

## 📊 测试用例执行

### 核心对话测试
```
测试输入："我记忆力很差，经常忘事"
期望输出：推荐记忆力训练链接 + 使用建议
验证点：链接正确性、建议专业性

测试输入："说话有困难，表达不清楚"  
期望输出：推荐语言功能训练链接 + 训练指导
验证点：功能匹配度、指导实用性

测试输入："心情很低落，不想训练"
期望输出：推荐情绪调节训练 + 心理支持
验证点：情感识别、支持温暖度

测试输入："想和其他病友交流"
期望输出：推荐老人论坛链接 + 使用说明
验证点：需求理解、功能推荐
```

### 边界情况测试
```
测试输入：无意义字符 "asdfgh"
期望输出：友好的默认回复 + 功能介绍

测试输入：空消息
期望输出：引导用户描述需求

测试输入：过长消息（>500字）
期望输出：正常处理并提取关键信息
```

## 🛠️ 故障排除指南

### 常见问题及解决方案

#### 1. 智能助手不显示
**症状**：页面加载后看不到智能助手按钮
**排查步骤**：
```javascript
// 1. 检查控制台错误
console.log('检查是否有 JavaScript 错误');

// 2. 验证文件加载
console.log('检查 load-assistant.js 是否正确加载');

// 3. 检查 CSS 样式
console.log('验证 ai-assistant.css 是否加载');
```
**解决方案**：
- 确认文件路径正确
- 检查网络连接
- 清除浏览器缓存

#### 2. Dify API 调用失败
**症状**：发送消息后无响应或报错
**排查步骤**：
```javascript
// 测试 API 连接
fetch('https://api.dify.ai/v1/chat-messages', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        inputs: {},
        query: '测试',
        user: 'test'
    })
}).then(response => {
    console.log('API 响应状态:', response.status);
    return response.json();
}).then(data => {
    console.log('API 响应数据:', data);
});
```
**解决方案**：
- 验证 API Key 正确性
- 检查应用 ID 配置
- 确认网络连接正常
- 查看 Dify 应用状态

#### 3. 链接跳转异常
**症状**：点击推荐链接无法正确跳转
**排查步骤**：
- 检查目标页面是否存在
- 验证链接路径正确性
- 测试相对路径配置

**解决方案**：
- 更新链接路径
- 检查页面文件完整性
- 调整路由配置

## 📈 性能优化建议

### 1. 加载速度优化
```javascript
// 懒加载智能助手组件
const loadAssistant = () => {
    if (!window.aiAssistantLoaded) {
        import('./components/ai-assistant.js')
            .then(() => {
                window.aiAssistantLoaded = true;
            });
    }
};

// 页面加载完成后再加载助手
document.addEventListener('DOMContentLoaded', loadAssistant);
```

### 2. API 调用优化
```javascript
// 添加请求缓存
const apiCache = new Map();

const callDifyAPI = async (message) => {
    const cacheKey = message.toLowerCase();
    if (apiCache.has(cacheKey)) {
        return apiCache.get(cacheKey);
    }
    
    const response = await fetch(/* API 调用 */);
    const result = await response.json();
    
    // 缓存结果（5分钟）
    apiCache.set(cacheKey, result);
    setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000);
    
    return result;
};
```

### 3. 用户体验优化
- 添加加载动画
- 实现打字机效果
- 支持消息重发
- 添加快捷回复按钮

## 🔄 持续维护

### 定期检查项目
- [ ] API 调用成功率监控
- [ ] 用户反馈收集分析
- [ ] 功能使用统计
- [ ] 错误日志分析

### 优化更新计划
1. **每周**：检查 API 状态和错误日志
2. **每月**：分析用户使用数据，优化提示词
3. **每季度**：更新知识库，扩展功能
4. **每年**：全面评估效果，制定改进计划

## 🎉 部署完成验证

当以下所有项目都通过时，说明智能体部署成功：

### 功能完整性 ✅
- 智能助手正常显示和交互
- 所有功能模块链接正确
- 对话响应准确专业
- 错误处理机制完善

### 用户体验 ✅  
- 界面美观易用
- 响应速度快（<3秒）
- 移动端适配良好
- 操作流程顺畅

### 技术稳定性 ✅
- API 调用成功率 >95%
- 无严重 JavaScript 错误
- 内存使用合理
- 兼容主流浏览器

---

🎊 **恭喜！康养平台智能体已成功部署！**

现在患者可以享受智能、温暖、专业的康复指导服务了。记得定期维护和优化，让智能体越来越聪明！
