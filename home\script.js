// 脑卒中康养平台 - 主页交互脚本

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializePage();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 启动动画效果
    startAnimations();
});

// 页面初始化
function initializePage() {
    // 隐藏加载动画
    setTimeout(() => {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 500);
        }
    }, 1500);
    
    // 初始化积分显示
    updatePointsDisplay();
    
    // 添加页面加载动画
    animatePageLoad();
}

// 绑定事件监听器
function bindEventListeners() {
    // 模块卡片点击事件
    const moduleCards = document.querySelectorAll('.module-card');
    moduleCards.forEach(card => {
        card.addEventListener('click', handleModuleClick);
        
        // 添加键盘支持
        card.setAttribute('tabindex', '0');
        card.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleModuleClick.call(this, e);
            }
        });
    });
    
    // 进入按钮点击事件
    const enterButtons = document.querySelectorAll('.enter-btn');
    enterButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const moduleCard = this.closest('.module-card');
            handleModuleClick.call(moduleCard, e);
        });
    });
    
    // 积分按钮点击事件
    const pointsBtn = document.querySelector('.points-btn');
    if (pointsBtn) {
        pointsBtn.addEventListener('click', handlePointsClick);
    }
    
    // 添加鼠标悬停音效（可选）
    addHoverEffects();
}

// 处理模块点击事件
function handleModuleClick(e) {
    const moduleType = this.getAttribute('data-module');
    const moduleName = this.querySelector('.module-title').textContent;
    
    // 添加点击动画
    this.style.transform = 'scale(0.95)';
    setTimeout(() => {
        this.style.transform = '';
    }, 150);
    
    // 显示加载提示
    showLoadingMessage(`正在进入${moduleName}...`);
    
    // 模拟页面跳转延迟
    setTimeout(() => {
        navigateToModule(moduleType, moduleName);
    }, 1000);
}

// 导航到具体模块
function navigateToModule(moduleType, moduleName) {
    // 这里可以根据实际需求进行页面跳转
    // 目前显示提示信息
    hideLoadingMessage();
    
    switch(moduleType) {
        case 'training':
            // 导航到训练康复页面
            window.location.href = '../training/index.html';
            break;
        case 'assessment':
            showModuleInfo('量表评估', '评估模块正在开发中，敬请期待！');
            break;
        case 'forum':
            showModuleInfo('老人论坛', '论坛模块正在开发中，敬请期待！');
            break;
        case 'videos':
            showModuleInfo('短视频频道', '视频模块正在开发中，敬请期待！');
            break;
        case 'points':
            showPointsDetail();
            break;
        default:
            showModuleInfo(moduleName, '模块正在开发中，敬请期待！');
    }
}

// 处理积分系统点击
function handlePointsClick() {
    showPointsDetail();
}

// 显示积分详情
function showPointsDetail() {
    const currentPoints = getCurrentPoints();
    const pointsHistory = getPointsHistory();
    
    const message = `
        <div style="text-align: left; line-height: 1.6;">
            <h3 style="color: #6ba3d6; margin-bottom: 15px;">积分详情</h3>
            <p><strong>当前积分：</strong> ${currentPoints} 分</p>
            <p><strong>本月获得：</strong> ${pointsHistory.thisMonth} 分</p>
            <p><strong>累计获得：</strong> ${pointsHistory.total} 分</p>
            <hr style="margin: 15px 0; border: none; border-top: 1px solid #eee;">
            <h4 style="color: #6ba3d6; margin-bottom: 10px;">获得积分方式：</h4>
            <ul style="margin-left: 20px;">
                <li>完成康复训练：+10分</li>
                <li>参与量表评估：+5分</li>
                <li>论坛发帖回复：+3分</li>
                <li>观看教学视频：+2分</li>
                <li>每日签到：+1分</li>
            </ul>
        </div>
    `;
    
    showCustomAlert('积分系统', message);
}

// 更新积分显示
function updatePointsDisplay() {
    const pointsElement = document.querySelector('.current-points strong');
    if (pointsElement) {
        const currentPoints = getCurrentPoints();
        pointsElement.textContent = currentPoints;
        
        // 添加数字动画效果
        animateNumber(pointsElement, 0, currentPoints, 1000);
    }
}

// 获取当前积分（模拟数据）
function getCurrentPoints() {
    // 从localStorage获取积分，如果没有则返回默认值
    return parseInt(localStorage.getItem('userPoints') || '128');
}

// 获取积分历史（模拟数据）
function getPointsHistory() {
    return {
        thisMonth: 45,
        total: 256
    };
}

// 数字动画效果
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// 页面加载动画
function animatePageLoad() {
    const elements = document.querySelectorAll('.module-card, .points-card');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 200 + 500);
    });
}

// 启动动画效果
function startAnimations() {
    // 心跳图标动画
    const heartIcon = document.querySelector('.header-icon');
    if (heartIcon) {
        setInterval(() => {
            heartIcon.style.transform = 'scale(1.1)';
            setTimeout(() => {
                heartIcon.style.transform = 'scale(1)';
            }, 200);
        }, 2000);
    }
}

// 添加悬停效果
function addHoverEffects() {
    const cards = document.querySelectorAll('.module-card, .points-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
    });
}

// 显示加载消息
function showLoadingMessage(message) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingText = loadingOverlay.querySelector('p');
    
    if (loadingText) {
        loadingText.textContent = message;
    }
    
    loadingOverlay.style.display = 'flex';
    loadingOverlay.classList.remove('hidden');
}

// 隐藏加载消息
function hideLoadingMessage() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('hidden');
    setTimeout(() => {
        loadingOverlay.style.display = 'none';
    }, 500);
}

// 显示模块信息
function showModuleInfo(title, message) {
    showCustomAlert(title, message);
}

// 自定义弹窗
function showCustomAlert(title, message) {
    // 创建弹窗元素
    const alertOverlay = document.createElement('div');
    alertOverlay.className = 'custom-alert-overlay';
    alertOverlay.innerHTML = `
        <div class="custom-alert">
            <div class="alert-header">
                <h3>${title}</h3>
                <button class="alert-close">&times;</button>
            </div>
            <div class="alert-content">
                ${message}
            </div>
            <div class="alert-footer">
                <button class="alert-btn">确定</button>
            </div>
        </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .custom-alert-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }
        
        .custom-alert {
            background: white;
            border-radius: 16px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }
        
        .alert-header {
            padding: 20px 25px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert-header h3 {
            margin: 0;
            color: #6ba3d6;
            font-size: 24px;
        }
        
        .alert-close {
            background: none;
            border: none;
            font-size: 28px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .alert-content {
            padding: 20px 25px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .alert-footer {
            padding: 15px 25px 20px;
            text-align: right;
        }
        
        .alert-btn {
            background: #6ba3d6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        
        .alert-btn:hover {
            background: #4a7ba7;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateY(-50px) scale(0.9); opacity: 0; }
            to { transform: translateY(0) scale(1); opacity: 1; }
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(alertOverlay);
    
    // 绑定关闭事件
    const closeBtn = alertOverlay.querySelector('.alert-close');
    const confirmBtn = alertOverlay.querySelector('.alert-btn');
    
    function closeAlert() {
        alertOverlay.style.animation = 'fadeIn 0.3s ease reverse';
        setTimeout(() => {
            document.body.removeChild(alertOverlay);
            document.head.removeChild(style);
        }, 300);
    }
    
    closeBtn.addEventListener('click', closeAlert);
    confirmBtn.addEventListener('click', closeAlert);
    alertOverlay.addEventListener('click', function(e) {
        if (e.target === alertOverlay) {
            closeAlert();
        }
    });
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
